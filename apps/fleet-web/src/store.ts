import { configureStore, getDefaultMiddleware } from '@reduxjs/toolkit'
import * as Sentry from '@sentry/browser'
import { createBrowserHistory } from 'history'
import { routerMiddleware } from 'connected-react-router'
import createSagaMiddleware from 'redux-saga'

import rootSaga from './sagas/root-saga'
import createRootReducer, { type AppState } from './root-reducer'
import { loggedOutFromAnotherTab } from 'duxs/user'
import { setReduxStoreSync } from './ReduxStoreSyncProvider'

const isDev = ENV.NODE_ENV === 'development'

const sagaMiddleware = createSagaMiddleware()

const history = createBrowserHistory()
const middleware = [routerMiddleware(history), sagaMiddleware]

middleware.push(
  ...getDefaultMiddleware({
    thunk: false,
    /**  May be enabled in the future once we only have serializable data in the store.
          Right now we are full of stuff like dates that are not serializable, for example.
      */
    serializableCheck: false,
    immutableCheck: isDev && ENV.ENABLE_REDUX_DEV_MIDDLEWARE === 'true',
  }),
)

const preloadedState = {
  providersSync: {
    historyInContext: history,
    queryClientInContext: null,
  },
} satisfies Partial<AppState>

const store = configureStore({
  reducer: createRootReducer(history),
  middleware,
  preloadedState,
  devTools: isDev && ENV.ENABLE_REDUX_DEV_MIDDLEWARE === 'true',
})

declare const module: any // https://github.com/gaearon/react-hot-loader/blob/master/docs/Troubleshooting.md#common-typescript-mistake
if (isDev && module.hot) {
  module.hot.accept('./root-reducer', () =>
    store.replaceReducer(createRootReducer(history)),
  )
}

setReduxStoreSync(store)
sagaMiddleware.run(rootSaga, { storeDispatch: store.dispatch, history })

export { history }
export default store

// Store user preferences that might have changed
window.addEventListener('beforeunload', () => {
  const state = store.getState()
  if (state.user.user && !state.user.user.isTokenAccount) {
    const sPreferences = JSON.stringify(state.user.preferences)
    const sVehicleFocusedLayerVisibility = JSON.stringify(
      state.map.vehicleFocusedLayerVisibility,
    )
    sessionStorage.setItem('userPreferences', sPreferences)
    localStorage.setItem('userPreferences' + state.user.user.id, sPreferences)
    sessionStorage.setItem(
      'vehicleFocusedLayerVisibility',
      sVehicleFocusedLayerVisibility,
    )
  } else {
    sessionStorage.removeItem('vehicleFocusedLayerVisibility')
  }
})

window.addEventListener('storage', (event) => {
  // T O D O: try/catch needed for IE11, doesn't properly fire storage event depending on size
  // Possible example: https://stackoverflow.com/questions/********/ie11-doesnt-fire-local-storage-events-when-value-is-too-large
  try {
    if (event.key === 'logoutFromOtherOpenTabs') {
      store.dispatch(loggedOutFromAnotherTab())
      localStorage.removeItem('logoutFromOtherOpenTabs')
    }
  } catch (error) {
    Sentry.captureException(error)
  }
})
