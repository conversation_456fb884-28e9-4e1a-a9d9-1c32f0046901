import { useCallback, useEffect, useState } from 'react'
import * as React from 'react'
import { debounce, isNumber } from 'lodash'
import { Stack } from '@karoo-ui/core'
import type { LatLngLiteral } from 'leaflet'
import { connect, useDispatch } from 'react-redux'
import { useLocation, type RouteComponentProps } from 'react-router-dom'
import screenfull from 'screenfull'

import { getMapTypeId, setNonGoogleMapTypeId, updateMapSize } from 'duxs/map'
import { getCustomLayerVisibility, setCustomLayerVisibility } from 'duxs/open-layers'
import {
  getLocale,
  getMapZoomOptions,
  getSettings_UNSAFE,
  getShowMapLivePositionButton,
} from 'duxs/user'
import { Map, MeasurePathDistance } from 'src/components/_map/_openstreetmap'
import { usePrevious } from 'src/hooks'
import { useEffectEvent } from 'src/hooks/useEventHandler'
import { clickedMapFullscreenButton } from 'src/modules/map-view/actions'
import {
  getPreviousPathname,
  type FullscreenMapNavigationState,
} from 'src/modules/map-view/fullscreen-map/slice'
import { useTypedSelector } from 'src/redux-hooks'
import type { AppState } from 'src/root-reducer'
import type { FixMeAny, ValueOf } from 'src/types'
import { MapTypeId } from 'src/types/extended/google-maps'
import LayersMenu from 'src/util-components/map/shared/layers-menu'
import MapControls from 'src/util-components/map/shared/map-controls'
import type { MapEventsInjectedProps } from 'src/util-components/map/shared/map-events'

import { shallowEqual, type VIEW_MODE } from 'cartrack-utils'
import { ctIntl } from '../../ctIntl'
import MapTrayToggles from '../shared/group-buttons/map-tray-toggles'
import LivePositionToggle from '../shared/live-position-toggle'
import type {
  MapProviderMetaData,
  UserAvailableMapApiProvider_Leaflet,
} from '../shared/types'
import {
  mapOpenLayersProviderConfig,
  type OpenLayersMapApiProvider,
} from './base-map-config'

type Props = {
  mapProviderMetaData: MapProviderMetaData<UserAvailableMapApiProvider_Leaflet>
  zoom?: number
  currentLayerVisibility: Record<string, FixMeAny>
  focusedItem?: Record<string, FixMeAny> | null
  hasFocusedItem?: boolean
  followFocusedItem: boolean
  isComparingTrips?: boolean
  showMapViewTray?: boolean
  showLayersMenu?: boolean
  showMapControls?: boolean
  isMeasuring: boolean
  fullscreenPath?: string
  fullscreenState?: FullscreenMapNavigationState
  isDrawingMap?: boolean
  onMeasureDistance?: (lat?: number, lng?: number) => void
  onFollowFocusedItemClick: () => void
  onMapLoaded?: (event: Record<string, FixMeAny>) => void
  onMapClick?: (event: L.LeafletMouseEvent) => void
  onFullscreenClick?: () => void
  onFullscreenEscape?: () => void
  onMoveStart?: () => void
  showFollowVehicleButton?: boolean
  children?: React.ReactNode
  viewTrayControls?: React.ReactElement
  changeViewMode?: (mode: ValueOf<typeof VIEW_MODE>) => void
} & Pick<
  MapEventsInjectedProps,
  | 'mapState'
  | 'center'
  | 'onContextMenuClick'
  | 'changeMapCenterZoom'
  | 'setVehicleFocusedLayerVisibility'
  | 'setLayerVisibility'
  | 'setMapState'
> &
  Pick<
    RouteComponentProps<any, any, { pathname: FixMeAny } | null | undefined>,
    'history'
  > &
  ReturnType<typeof mapStateToProps> &
  typeof mapDispatchToProps

const OpenLayerBaseMap = ({
  center,
  config,
  children,
  currentLayerVisibility: {
    maps: showMaps,
    traffic: showTraffic,
    transit: showTransit,
  },
  defaultMapZoom,
  focusedItem,
  followFocusedItem,
  hasFocusedItem = false,
  onMeasureDistance,
  isComparingTrips = false,
  isDrawingMap = false,
  locale,
  mapScheme,
  mapState,
  onFollowFocusedItemClick,
  showFollowVehicleButton = true,
  setCustomLayerVisibility,
  // viewTrayControls,
  changeViewMode,
  zoom,
  setVehicleFocusedLayerVisibility,
  changeMapCenterZoom,
  setLayerVisibility,
  setMapState,
  updateMapSize,
  fullscreenPath,
  onMapLoaded,
  onContextMenuClick,
  onMapClick,
  isMeasuring,
  showMapViewTray = true,
  showLayersMenu = true,
  showMapControls = true,
  fullscreenState,
  customLayerVisibility = {},
  onFullscreenClick,
  // onFullscreenEscape,
  onMoveStart,
  history,
  mapZoomOptions,
  mapProviderMetaData: mapProviderMetaDataProp,
  showMapLivePositionButton,
  mapTypeId,
}: Props) => {
  const dispatch = useDispatch()
  const location = useLocation()
  const mapProviderMetaData =
    mapProviderMetaDataProp as MapProviderMetaData<OpenLayersMapApiProvider>
  const mapApiProviderId = mapProviderMetaData.currentMapProvider
  const prevFocusedItem = usePrevious(focusedItem)
  const previousPathname = useTypedSelector(getPreviousPathname)
  const [isCenterControlled, setIsCenterControlled] = useState(false)
  const [isZoomFromMapControls, setIsZoomFromMapControls] = useState(false)
  const [mapInstance, setMapInstance] = useState<L.Map | null>(null)
  const [mapOptions, setMapOptions] = useState<{
    center: LatLngLiteral
    zoom: number
    minZoom: number
    maxZoom: number
  }>({
    center: center ? center : ({ lat: undefined, lng: undefined } as FixMeAny),
    zoom: zoom ? zoom : defaultMapZoom,
    minZoom: mapZoomOptions.minZoom,
    maxZoom: mapZoomOptions.maxZoom,
  })

  const resetMapOptions = useCallback(() => {
    if (
      !isDrawingMap &&
      (!isCenterControlled || isZoomFromMapControls || followFocusedItem) &&
      (!shallowEqual(mapOptions.center, center) || mapOptions.zoom !== zoom)
    ) {
      setMapOptions({
        ...mapOptions,
        center,
        zoom: zoom ? zoom : defaultMapZoom,
      })
      if (hasFocusedItem) {
        setIsCenterControlled(true)
      }
      setIsZoomFromMapControls(false)

      return
    }
    if (
      center &&
      isNumber(center.lat) &&
      isNumber(center.lng) &&
      isNumber(zoom) &&
      (!shallowEqual(mapOptions.center, center) || mapOptions.zoom !== zoom)
    ) {
      setMapOptions({
        ...mapOptions,
        center,
        zoom: zoom ? zoom : defaultMapZoom,
      })
    }
  }, [
    center,
    defaultMapZoom,
    followFocusedItem,
    hasFocusedItem,
    isCenterControlled,
    isDrawingMap,
    isZoomFromMapControls,
    mapOptions,
    zoom,
  ])

  const onFullscreenChange = useEffectEvent(() => {
    if (!screenfull) {
      return
    }
    if (
      fullscreenPath &&
      location.pathname === fullscreenPath &&
      !screenfull.isFullscreen &&
      previousPathname
    ) {
      history.push(previousPathname)
    }
  })

  useEffect(() => {
    if (screenfull) {
      screenfull.on('change', onFullscreenChange)
    }
    return () => {
      if (screenfull) {
        screenfull.off('change', onFullscreenChange)
      }
    }
  }, [])

  useEffect(() => resetMapOptions(), [center, resetMapOptions, zoom])

  useEffect(() => {
    if (!hasFocusedItem || (focusedItem && prevFocusedItem !== focusedItem)) {
      setIsCenterControlled(false)
    }
  }, [hasFocusedItem, focusedItem, prevFocusedItem])

  const handleMapSchemeChange = (selectedMapTypeId: google.maps.MapTypeId) => {
    const trafficTransit = {
      traffic: selectedMapTypeId === MapTypeId.HYBRID,
      transit: false,
    }
    dispatch(setNonGoogleMapTypeId(selectedMapTypeId))
    setCustomLayerVisibility(
      selectedMapTypeId !== MapTypeId.ROADMAP ? trafficTransit : {},
    )
  }

  const handleMapCenterZoomChange = (lat: number, lng: number, zoom: number) => {
    setIsZoomFromMapControls(true)
    changeMapCenterZoom(lat, lng, zoom, 'handleMapCenterZoomChange')
  }

  const { layers: mapOpenLayers } = mapOpenLayersProviderConfig[mapApiProviderId]
  const selectedLayer =
    // eslint-disable-next-line no-nested-ternary
    showMaps && showTraffic
      ? 'traffic'
      : showMaps && showTransit
        ? 'transit'
        : undefined

  const openMapsLayers = {
    maps: {
      children: mapOpenLayers.map((layer) => {
        const disabled = mapScheme !== 'roadmap' && layer !== 'alerts'
        return { key: layer, disabled }
      }),
    },
  }

  const measureDistanceLabels = {
    measureLabel: ctIntl.formatMessage({ id: `Measured Distance` }),
    measureStopLabel: ctIntl.formatMessage({
      id: `Press any key to stop measuring.`,
    }),
    metricLabel: ctIntl.formatMessage({ id: `km` }),
  }

  const handleLayerVisibilityChange = (update: Record<string, FixMeAny>) => {
    const key = Object.keys(update)[0]
    const otherLayer =
      // eslint-disable-next-line no-nested-ternary
      key === 'traffic' && update[key]
        ? { transit: false }
        : key === 'transit' && update[key]
          ? { traffic: false }
          : {}

    if (hasFocusedItem || isComparingTrips) {
      setVehicleFocusedLayerVisibility({ ...update, ...otherLayer })
    } else {
      setLayerVisibility({ ...update, ...otherLayer })
    }
  }

  const handleMapStateChange = debounce((event: { target: L.Map }) => {
    const mapBounds = event.target.getBounds()
    const nw = mapBounds.getNorthWest()
    const se = mapBounds.getSouthEast()
    const size = event.target.getSize()

    const obj = {
      bounds: { nw, se },
      center: event.target.getCenter(),
      size: { width: size.x, height: size.y },
      zoom: event.target.getZoom(),
    }

    if (
      !mapState ||
      obj.center.lat !== mapState.center.lat ||
      obj.center.lng !== mapState.center.lng ||
      obj.zoom !== zoom ||
      obj.bounds.nw !== mapState.bounds.nw ||
      obj.bounds.se !== mapState.bounds.se
    ) {
      console.log('Map onChange', zoom, obj.zoom)
      changeMapCenterZoom(obj.center.lat, obj.center.lng, obj.zoom, 'handleMapChange')
      setMapState(obj as FixMeAny)
      updateMapSize(obj.size, obj.bounds)
    }
  }, 100)

  const handleMapLoaded = (map: L.Map) => {
    const event = { target: map }
    handleMapStateChange(event)
    if (onMapLoaded) {
      onMapLoaded(event)
    }
    if (!mapInstance) {
      setMapInstance(map)
    }
  }

  const handleMapRightClick = (event: L.LeafletMouseEvent) => {
    const position = {
      lat: event.latlng.lat,
      lng: event.latlng.lng,
      x: event.containerPoint.x,
      y: event.containerPoint.y,
    }
    onContextMenuClick('', '', position)
  }

  const handleFullscreenClick = () => {
    if (screenfull) {
      const { isFullscreen } = screenfull

      if (onFullscreenClick) {
        onFullscreenClick()
      }

      if (isFullscreen) {
        history.push(previousPathname)
        screenfull.exit()
      } else {
        screenfull.request()
        dispatch(
          clickedMapFullscreenButton({
            fullscreenMapState: fullscreenState,
            prePathname: location.pathname,
            fullscreenPath,
            history,
          }),
        )
      }
    }
  }

  return (
    <>
      <div className="OpenLayersMap">
        <Map
          key={1}
          options={mapOptions}
          onMapClick={onMapClick}
          onMapContextMenu={handleMapRightClick}
          onMapChange={handleMapStateChange}
          onMapLoaded={handleMapLoaded}
          onMoveStart={onMoveStart}
          locale={locale}
          mapApiProviderId={mapApiProviderId}
          mapScheme={mapScheme}
          mapLayer={selectedLayer}
          config={config}
          editable={isDrawingMap}
        >
          {children}
          {isMeasuring && mapInstance && (
            <MeasurePathDistance
              mapInstance={mapInstance}
              onEndMeasure={onMeasureDistance}
              {...measureDistanceLabels}
            />
          )}
        </Map>
      </div>
      <Stack
        sx={{
          position: 'absolute',
          right: '20px',
          top: '20px',
          flexDirection: 'row',
          gap: 1,
        }}
      >
        {showMapViewTray && (
          <MapTrayToggles
            toggleFullscreen={handleFullscreenClick}
            toggleLandscape={(mode) => changeViewMode && changeViewMode(mode)}
            togglePortrait={(mode) => changeViewMode && changeViewMode(mode)}
          />
        )}
        {showMapLivePositionButton && <LivePositionToggle />}
        {showLayersMenu && (
          <LayersMenu
            hasFocusedItem={hasFocusedItem}
            customLayerVisibility={customLayerVisibility}
            openMapsLayers={openMapsLayers}
            setLayerVisibility={handleLayerVisibilityChange}
          />
        )}
      </Stack>

      {showMapControls && (
        <MapControls
          mapTypeId={mapTypeId}
          mapProviderSelectionUI={
            mapProviderMetaData.selectionUI === 'do_not_show'
              ? 'do_not_show'
              : {
                  ...mapProviderMetaData.selectionUI,
                  currentMapProvider:
                    mapApiProviderId as UserAvailableMapApiProvider_Leaflet,
                }
          }
          center={mapOptions.center}
          hasFocusedItem={hasFocusedItem}
          onChangeMapTypeId={handleMapSchemeChange}
          onChangeMapCenterZoom={handleMapCenterZoomChange}
          showMapTypeControls
          {...(showFollowVehicleButton && {
            followFocusedItem,
            onFollowFocusedItemClick,
          })}
          zoom={mapOptions.zoom}
          fullscreenUI="do_not_show"
        />
      )}
    </>
  )
}

const mapStateToProps = (state: AppState) => {
  const { defaultMapZoom, hereApiCode } = getSettings_UNSAFE(state)
  const config = {
    apiKey: hereApiCode,
  }

  return {
    config,
    customLayerVisibility: getCustomLayerVisibility(state),
    defaultMapZoom: defaultMapZoom as number,
    locale: getLocale(state),
    mapScheme: getMapTypeId(state),
    mapZoomOptions: getMapZoomOptions(state),
    showMapLivePositionButton: getShowMapLivePositionButton(state),
    mapTypeId: getMapTypeId(state),
  }
}

const mapDispatchToProps = {
  setCustomLayerVisibility,
  updateMapSize,
}

export default connect(mapStateToProps, mapDispatchToProps)(OpenLayerBaseMap)
