import { createRoot } from 'react-dom/client'
import { useState, useEffect } from 'react'

import './initialize-critical-code'
import { Provider } from 'react-redux'
import { ReactQueryDevtools } from '@tanstack/react-query-devtools'
import { persistStore } from 'redux-persist'
import { PersistGate } from 'redux-persist/integration/react'
import AppRoot from './app-root'
import { HTML5Backend } from 'react-dnd-html5-backend'
import { DndProvider } from 'react-dnd'
import { ConnectedRouter } from 'connected-react-router'
import { shouldPolyfill as shouldPolyfillLocale } from '@formatjs/intl-locale/should-polyfill'
import { queryClient } from './RouterRoot'

import store, { history } from './store'
import {
  HistoryProviderWithReduxSync,
  QueryClientProviderWithReduxSync,
} from './providers'

export default store

function App() {
  // Based on https://twitter.com/tannerlinsley/status/1392857647037091849
  const [showReactQueryDevtools, setShowReactQueryDevtools] = useState(false)

  useEffect(() => {
    ;(window as any).toggleDevtools = () => setShowReactQueryDevtools((old) => !old)
  }, [])

  return (
    <Provider store={store}>
      <PersistGate
        loading={null}
        persistor={persistStore(store)}
        onBeforeLift={() => console.log('Redux Persist is ready')}
      >
        <DndProvider backend={HTML5Backend}>
          <QueryClientProviderWithReduxSync client={queryClient}>
            <HistoryProviderWithReduxSync history={history}>
              {showReactQueryDevtools ? <ReactQueryDevtools initialIsOpen /> : null}
              <ConnectedRouter history={history}>
                <AppRoot />
              </ConnectedRouter>
            </HistoryProviderWithReduxSync>
          </QueryClientProviderWithReduxSync>
        </DndProvider>
      </PersistGate>
    </Provider>
  )
}

const renderApp = () => {
  const container = document.getElementById('app')
  if (!container) {
    throw new Error('No container found to render the app')
  }
  const root = createRoot(container)
  root.render(<App />)
}

// For more info see https://formatjs.io/docs/polyfills
const polyfills = []
if (shouldPolyfillLocale()) {
  polyfills.push(import('@formatjs/intl-locale/polyfill'))
}

Promise.all(polyfills).then(() => {
  if (ENV.CYPRESS_CT_ENV === null) {
    return renderApp()
  }
  return null
})
