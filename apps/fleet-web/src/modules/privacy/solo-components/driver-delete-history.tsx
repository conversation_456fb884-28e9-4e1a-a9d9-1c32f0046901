import { PureComponent } from 'react'
import { filter, find } from 'lodash'
import memoizeOne from 'memoize-one'
import { FormattedMessage, injectIntl, type IntlShape } from 'react-intl'
import { connect } from 'react-redux'

import { getDriverManualDeleteHistoryDetail } from 'duxs/privacy'
import type { FixMeAny } from 'src/types'
import type { StrictTypedColumn, TypedCellInfo } from 'src/types/extended/react-table'
import { makeSanitizedInnerHtmlProp } from 'src/util-functions/security-utils'

import { generateImgUriFromBase64 } from 'cartrack-utils'
import { AdvancedTable, Button, ctIntl, Spinner } from 'cartrack-ui-kit'
import avatarIcon from '../../../../assets/svg/avatar.svg'
import { formatDate } from '../../../util-functions/moment-helper'
import GDPRHeader from './gdpr-header'
import GDPRHeaderList from './gdpr-header-list'
import type { DeleteHistoryItem } from './types'

type Props = {
  scrollCheckId: string
  intl: IntlShape
} & ReturnType<typeof mapStateToProps> &
  typeof actionCreators

type State = {
  searchList: Array<FixMeAny>
  dataList: Array<FixMeAny> | undefined
  activeDriver:
    | {
        avatar: string
        driver_name: string
        create_ts: boolean
        client_driver_id: string
      }
    | undefined
  activeSearch: string
  isSearching?: boolean
}

class DriverDeleteHistory extends PureComponent<Props, State> {
  static defaultProps = {
    driverManualDeleteList: [],
    privacyStatus: [],
    driverDeleteHistoryData: [],
  }

  constructor(props: Props) {
    super(props)

    this.state = {
      searchList: [],
      dataList: props.driverManualDeleteList,
      activeDriver: undefined,
      activeSearch: '',
    }
  }

  onSearchChange = (value: string) => {
    if (value === '') {
      return this.setState({
        ...this.state,
        isSearching: false,
        activeSearch: value,
      })
    }

    return this.setState({
      ...this.state,
      activeSearch: value,
      isSearching: true,
    })
  }

  onItemSelected = (item: FixMeAny) => {
    this.setState(
      {
        ...this.state,
        activeDriver: item,
      },
      () =>
        this.props.getDriverManualDeleteHistoryDetail({
          clientDriverID: item.client_driver_id,
        }),
    )
  }

  detailTableClose = () => {
    this.setState({
      ...this.state,
      activeDriver: undefined,
    })
  }

  makeTableData = memoizeOne(
    (
      isSearching: boolean | undefined,
      propsData: Props['driverManualDeleteList'],
      activeSearch: string,
    ) => {
      let items = []

      if (isSearching) {
        for (const propsDatum of propsData) {
          if (
            propsDatum.driver_name.toLowerCase().includes(activeSearch.toLowerCase())
          ) {
            items.push(propsDatum)
          }
        }
      } else {
        items = propsData
      }

      return items
    },
  )

  renderTimeCell = (row: TypedCellInfo<DeleteHistoryItem>, val: 'start' | 'end') => {
    if (val === 'start') {
      return formatDate(row.original.start_timestamp, 'hh:mm A')
    }

    return formatDate(row.original.end_timestamp, 'hh:mm A')
  }

  renderDateCell = (row: TypedCellInfo<DeleteHistoryItem>) =>
    `${formatDate(
      row.original.start_timestamp,
      (this.props.intl.messages['util.dateFormat'] as string).toUpperCase(),
    )} - ${formatDate(
      row.original.end_timestamp,
      (this.props.intl.messages['util.dateFormat'] as string).toUpperCase(),
    )}`

  renderPrivacyCell = (row: TypedCellInfo<DeleteHistoryItem>) => {
    let str = ''

    const { privacyStatus } = this.props

    const obj = find(
      privacyStatus,
      (o) => o.privacy_process_status_id === row.original.privacy_process_status_id,
    )

    if (obj) {
      str = obj.description
    }

    return ctIntl.formatMessage({ id: str })
  }

  columnsForm = (): Array<
    StrictTypedColumn<Props['driverDeleteHistoryData'][number]>
  > => [
    {
      Header: 'Date',
      id: 'date',
      accessor: (o) => o.start_timestamp,
      Cell: (row) => this.renderDateCell(row),
      className: 'util-textCenter util-justifyAuto',
    },
    {
      Header: 'Start Time',
      id: 'start_time',
      accessor: (o) => o.start_timestamp,
      Cell: (row) => this.renderTimeCell(row, 'start'),
      className: 'util-textCenter util-justifyAuto',
    },
    {
      Header: 'End Time',
      id: 'end_time',
      accessor: (o) => o.end_timestamp,
      Cell: (row) => this.renderTimeCell(row, 'end'),
      className: 'util-textCenter util-justifyAuto',
    },
    {
      Header: 'Privacy Status',
      id: 'privacy_status',
      accessor: (o) => o.privacy_process_status_id,
      Cell: (row) => this.renderPrivacyCell(row),
      className: 'util-textCenter util-justifyAuto',
    },
  ]

  _getHistoryTable() {
    const { activeDriver } = this.state
    const { driverDeleteHistoryDataLoading, driverDeleteHistoryData } = this.props

    if (driverDeleteHistoryDataLoading) {
      return <Spinner />
    }

    if (activeDriver && driverDeleteHistoryData.length > 0) {
      const columns = this.columnsForm()
      return (
        <div className="util-p15">
          <div className="gdpr-table-wrapper util-p15">
            <div className="gdpr-table-header">
              {activeDriver.avatar ? (
                <img
                  className="gdpr-avatar-icon"
                  src={generateImgUriFromBase64(activeDriver.avatar)}
                  alt={activeDriver.driver_name}
                />
              ) : (
                <div
                  className="gdpr-avatar-icon"
                  {...makeSanitizedInnerHtmlProp({ dirtyHtml: avatarIcon })}
                />
              )}
              <div className="header-name-wrapper">
                <p>{activeDriver.driver_name}</p>
              </div>
              <Button
                icon="times"
                className="header-button-wrapper"
                onClick={this.detailTableClose}
              />
            </div>
            <AdvancedTable
              columns={columns}
              data={filter(driverDeleteHistoryData, (o) => o.create_ts)}
              showPagination
              defaultPageSize={10}
              minRows={10}
              className="FormDetailView-table FormDetailView-table--suppliers"
              disableSelect
            />
          </div>
        </div>
      )
    }

    if (activeDriver && !activeDriver.create_ts) {
      return (
        <div className="uitl-p15 util-textCenter">
          <FormattedMessage
            tagName="p"
            id="This driver has no privacy history to show"
          />
        </div>
      )
    }

    return null
  }

  resetSelection = () => this.setState({ activeDriver: undefined })

  render() {
    const { isSearching, activeDriver, activeSearch } = this.state
    const { driverManualDeleteLoading, driverManualDeleteList, scrollCheckId } =
      this.props

    if (driverManualDeleteLoading) {
      return <Spinner />
    }

    const memoData = this.makeTableData(
      isSearching,
      driverManualDeleteList,
      activeSearch,
    )

    return (
      <>
        <GDPRHeader
          label={'Deleted Driver Privacy History'}
          onSearchChange={this.onSearchChange}
          searchPlaceholder="Search Driver ..."
        />
        <GDPRHeaderList
          scrollCheckId={scrollCheckId}
          dataList={memoData}
          keyId={'client_driver_id'}
          activeDriver={activeDriver}
          itemClassName={'gdpr-driver-history'}
          onItemSelected={this.onItemSelected}
          resetSelection={this.resetSelection}
        />
        {this._getHistoryTable()}
      </>
    )
  }
}

function mapStateToProps(state: {
  privacy: {
    driverManualDeleteLoading: boolean
    driverManualDeleteList: Array<{
      driver_name: string
    }>
    privacyStatus: Array<FixMeAny>
    driverDeleteHistoryData: Array<DeleteHistoryItem>
    driverDeleteHistoryDataLoading: boolean
  }
}) {
  return {
    driverManualDeleteLoading: state.privacy.driverManualDeleteLoading,
    driverManualDeleteList: state.privacy.driverManualDeleteList,
    driverDeleteHistoryData: state.privacy.driverDeleteHistoryData,
    driverDeleteHistoryDataLoading: state.privacy.driverDeleteHistoryDataLoading,
    privacyStatus: state.privacy.privacyStatus,
  }
}

const actionCreators = {
  getDriverManualDeleteHistoryDetail,
}

export default connect(mapStateToProps, actionCreators)(injectIntl(DriverDeleteHistory))
