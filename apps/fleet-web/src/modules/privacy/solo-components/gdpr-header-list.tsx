import { PureComponent } from 'react'
import { get, uniqueId } from 'lodash'
import { FormattedMessage } from 'react-intl'
import Switch from 'react-switch'
import VirtualList from 'react-tiny-virtual-list'

import Icon from 'src/components/Icon'
import type { FixMeAny } from 'src/types'
import VehicleIconCircle from 'src/util-components/vehicle-icon-circle'
import { makeSanitizedInnerHtmlProp } from 'src/util-functions/security-utils'

import { generateImgUriFromBase64 } from 'cartrack-utils'
import avatarIcon from '../../../../assets/svg/avatar.svg'
import idTagIcon from '../../../../assets/svg/key.svg'
import { ALIGNMENT, DIRECTION } from './constants'

// eslint-disable-next-line no-unneeded-ternary
const parseScheduleIdExistence = (id: string) => (id ? true : false)

const _getDriverName = (item: { driver_name: string }) =>
  item.driver_name && item.driver_name !== '' ? (
    ` ${item.driver_name}`
  ) : (
    <FormattedMessage id="Driver" />
  )

type Props = {
  dataList: Array<FixMeAny>
  hasTick?: (
    checked: boolean,
    item: FixMeAny,
    event: React.SyntheticEvent<MouseEvent | KeyboardEvent> | MouseEvent | undefined,
  ) => void
  keyId: string
  onItemSelected?: (selectedItem: FixMeAny) => void
  itemClassName: string
  resetSelection?: () => void
  scrollCheckId: string
  onMultipleSelected?: (multipleToGo: Record<string, FixMeAny> | undefined) => void
  activeVehicle?: FixMeAny
  multipleSelection?: Record<string, any>
  activeDriver?: { client_driver_id: string }
  activeTag?: { identification_tag_id: string }
}

type State = {
  selected: number
  visibleIndex: number
  activeDriver?: { client_driver_id: string }
  activeVehicle?: FixMeAny
  activeTag?: { identification_tag_id: string }
  multipleSelectAvailable: boolean
  multipleSelection: Record<string, FixMeAny> | undefined
}

class GDPRHeaderList extends PureComponent<Props, State> {
  static defaultProps = {
    dataList: [],
    hasTick: null,
    onItemSelected: null,
    resetSelection: null,
    onMultipleSelected: null,
    activeVehicle: {},
    multipleSelection: {},
  }

  constructor(props: Props) {
    super(props)
    this.state = {
      selected: 0,
      visibleIndex: 10,
      multipleSelectAvailable: false,
      multipleSelection: {},
    }
  }

  static getDerivedStateFromProps(nextProps: Props, prevState: State) {
    if (nextProps.activeDriver && prevState.activeDriver !== nextProps.activeDriver) {
      return {
        selected: nextProps.activeDriver.client_driver_id,
        activeArray: 'activeDriver',
      }
    }

    if (
      get(nextProps.activeVehicle, 'vehicle_id') !==
      get(prevState.activeVehicle, 'vehicle_id')
    ) {
      return {
        selected: nextProps.activeVehicle.vehicle_id,
        activeArray: 'activeVehicle',
      }
    }

    if (nextProps.activeTag && prevState.activeTag !== nextProps.activeTag) {
      return {
        selected: nextProps.activeTag.identification_tag_id,
        activeArray: 'activeTag',
      }
    }

    return { selected: 0, activeArray: undefined }
  }

  componentDidMount() {
    const { scrollCheckId } = this.props
    let interval_right: number | undefined
    let interval_left: number | undefined
    const prevElement: HTMLElement = document.querySelector(
      `#gdpr-arrow-prev_${scrollCheckId}`,
    ) as HTMLElement
    const nextElement: HTMLElement = document.querySelector(
      `#gdpr-arrow-next_${scrollCheckId}`,
    ) as HTMLElement

    prevElement.onmousedown = () => {
      interval_left = window.setInterval(() => this.onArrowClick('left', undefined), 50)
    }

    prevElement.onmouseup = () => {
      window.clearInterval(interval_left)
      interval_left = undefined
    }

    prevElement.onmouseout = () => {
      window.clearInterval(interval_left)
      interval_left = undefined
    }

    nextElement.onmousedown = () => {
      interval_right = window.setInterval(
        () => this.onArrowClick('right', scrollCheckId),
        50,
      )
    }

    nextElement.onmouseup = () => {
      window.clearInterval(interval_right)
      interval_right = undefined
    }

    nextElement.onmouseout = () => {
      window.clearInterval(interval_right)
      interval_right = undefined
    }
  }

  componentDidUpdate(prevProps: Props) {
    if (prevProps.dataList.length !== this.props.dataList.length) {
      this._resetListOffSet()
    }

    this.setState({
      multipleSelectAvailable: this.state.selected !== 0,
      multipleSelection: this.props.multipleSelection,
    })
  }

  _resetListOffSet = () =>
    this.setState({ visibleIndex: 10, selected: 0, multipleSelection: {} }, () => {
      if (this.props.resetSelection) {
        this.props.resetSelection()
      }
    })

  onChangeTick = (
    checked: boolean,
    item: FixMeAny,
    event: React.SyntheticEvent<MouseEvent | KeyboardEvent> | MouseEvent | undefined,
  ) => {
    const { hasTick } = this.props
    if (hasTick) {
      hasTick(checked, item, event)
    }
  }

  onSelect = (key: number) => {
    const { keyId, onItemSelected, dataList, onMultipleSelected } = this.props
    const { multipleSelection, multipleSelectAvailable } = this.state

    const selectedItem = dataList.find((o) => o[keyId] === key)

    const multipleToGo = JSON.parse(JSON.stringify(multipleSelection))

    if (multipleSelectAvailable && multipleToGo[key]) {
      delete multipleToGo[key]
      return this.setState({ multipleSelection: multipleToGo }, () => {
        if (onMultipleSelected) {
          onMultipleSelected(multipleToGo)
        }
      })
    }

    if (multipleSelectAvailable && onMultipleSelected) {
      onMultipleSelected(multipleToGo)
    }

    this.setState({ selected: key, multipleSelection: {} }, () => {
      if (onItemSelected) {
        onItemSelected(selectedItem)

        if (onMultipleSelected) {
          onMultipleSelected(undefined)
        }
      }
    })
  }

  _offsetMultiplier = (
    direction: 'left' | 'right',
    scrollCheckId: string | undefined,
  ) => {
    const { visibleIndex } = this.state
    const { itemClassName } = this.props

    if (direction === 'left' && visibleIndex <= 10) {
      return 10
    }

    if (direction === 'right') {
      const theFather: HTMLElement = document.querySelector(
        `#${scrollCheckId}`,
      ) as HTMLElement
      const theSon = theFather.querySelectorAll('div')[0]
      const theGrandSon = theSon.querySelectorAll('div')[0]
      const listVirtualWidth = Number.parseInt(
        theGrandSon.style.width.replace('px', ''),
        10,
      )
      if (
        itemClassName === 'gdpr-driver-manual' ||
        itemClassName === 'gdpr-driver-id-tag'
      ) {
        if (visibleIndex + 300 > listVirtualWidth - 700) {
          return visibleIndex
        }
      } else {
        // eslint-disable-next-line no-lonely-if, sonarjs/no-lonely-if
        if (visibleIndex + 200 > listVirtualWidth - 500) {
          return visibleIndex
        }
      }
    }

    if (
      itemClassName === 'gdpr-driver-manual' ||
      itemClassName === 'gdpr-driver-id-tag'
    ) {
      if (visibleIndex <= 10 && direction !== 'left') {
        return 700
      }

      if (direction === 'right') {
        return visibleIndex + 300
      }

      if (direction === 'left') {
        return visibleIndex - 300
      }
    } else {
      if (visibleIndex <= 0 && direction !== 'left') {
        return 500
      }

      if (direction === 'right') {
        return visibleIndex + 200
      }

      if (direction === 'left') {
        return visibleIndex - 200
      }
    }

    return visibleIndex
  }

  _cellWithFromItemClassName = (itemClassName: string) => {
    switch (itemClassName) {
      case 'gdpr-driver-manual':
      case 'gdpr-driver-id-tag': {
        return 340
      }
      case 'gdpr-driver-history':
      case 'gdpr-driver-tag-history':
      case 'gdpr-vehicle-total':
      case 'gdpr-vehicle-settings':
      case 'gdpr-vehicle-location': {
        return 200
      }
      default: {
        return 0
      }
    }
  }

  _tableHeightFromItemClassName = (itemClassName: string) => {
    switch (itemClassName) {
      case 'gdpr-driver-manual':
      case 'gdpr-driver-id-tag': {
        return 87
      }
      case 'gdpr-driver-history':
      case 'gdpr-driver-tag-history':
      case 'gdpr-vehicle-total':
      case 'gdpr-vehicle-settings':
      case 'gdpr-vehicle-location': {
        return 145
      }
      default: {
        return 0
      }
    }
  }

  isOnMultipleSelection = (itemId: number) =>
    this.state.multipleSelection && this.state.multipleSelection[itemId]

  addToMultipleSelection = (
    e: React.MouseEvent<Element, MouseEvent>,
    item: FixMeAny,
    keyId: string,
  ) => {
    e.stopPropagation()
    const multipleSelection = JSON.parse(JSON.stringify(this.state.multipleSelection))

    const { onMultipleSelected } = this.props

    multipleSelection[item[keyId]] = item
    this.setState({ multipleSelection }, () => {
      if (onMultipleSelected) {
        onMultipleSelected({
          [item[keyId]]: item,
          ...multipleSelection,
        })
      }
    })
  }

  // eslint-disable-next-line complexity, sonarjs/sonar-max-params
  _getProperCell = (
    item: FixMeAny,
    selected: number,
    itemClassName: string,
    innerKey: number,
    onChangeTick: (
      checked: boolean,
      item: FixMeAny,
      event: React.SyntheticEvent<MouseEvent | KeyboardEvent> | MouseEvent | undefined,
    ) => void,
    onSelect: (key: number) => void,
    multipleSelectAvailable: boolean,
    keyId: string,
  ) => {
    switch (itemClassName) {
      case 'gdpr-driver-manual': {
        return (
          <div
            id={uniqueId(`${innerKey}_`)}
            key={uniqueId(`${innerKey}_`)}
            className={`menu-item ${itemClassName} ${
              selected === innerKey ? 'selected' : ''
            }`}
            onClick={() => onSelect(innerKey)}
          >
            <div className={'image-wrapper'}>
              {item.avatar ? (
                <img
                  className="gdpr-driver-icon util-noselect"
                  src={generateImgUriFromBase64(item.avatar)}
                  alt=""
                />
              ) : (
                <div
                  className="gdpr-driver-icon util-noselect"
                  {...makeSanitizedInnerHtmlProp({ dirtyHtml: avatarIcon })}
                />
              )}
            </div>
            <div className={'name-wrapper-id-tags'}>
              <p className="util-noselect util-noMargin">{_getDriverName(item)}</p>
              <p className="id-tag-sub-label util-noselect">
                <span className="util-semicolon">
                  <FormattedMessage id="Registration" />
                </span>
                {item.registration ? (
                  item.registration
                ) : (
                  <span>
                    <FormattedMessage id="No Vehicle" />
                  </span>
                )}
              </p>
            </div>
            <Switch
              onChange={(checked: boolean) => onChangeTick(checked, item, undefined)}
              checked={item.current_privacy_state === 't'}
              id={`${innerKey}`}
              checkedIcon={<span className="privacy-switch-label active">ON</span>}
              uncheckedIcon={<span className="privacy-switch-label inactive">OFF</span>}
              height={20}
              onColor={'#B5D55E'}
              offColor={'#999999'}
            />
          </div>
        )
      }

      case 'gdpr-driver-history': {
        return (
          <div
            id={uniqueId(`${innerKey}_`)}
            key={uniqueId(`${innerKey}_`)}
            className={`menu-item ${itemClassName} ${
              selected === innerKey ? 'selected' : ''
            }`}
            onClick={() => onSelect(innerKey)}
          >
            <div className={'image-wrapper'}>
              {item.avatar ? (
                <img
                  className="gdpr-driver-icon util-noselect list"
                  src={generateImgUriFromBase64(item.avatar)}
                  alt=""
                />
              ) : (
                <div
                  className="gdpr-driver-icon util-noselect"
                  {...makeSanitizedInnerHtmlProp({ dirtyHtml: avatarIcon })}
                />
              )}
            </div>
            <div className={'name-wrapper'}>
              <p className="util-noselect">{_getDriverName(item)}</p>
            </div>
          </div>
        )
      }

      case 'gdpr-driver-tag-history': {
        return (
          <div
            id={uniqueId(`${innerKey}_`)}
            key={uniqueId(`${innerKey}_`)}
            className={`menu-item ${itemClassName} ${
              selected === innerKey ? 'selected' : ''
            }`}
            onClick={() => onSelect(innerKey)}
          >
            <div className={'image-wrapper-id-tag'}>
              <div
                className="gdpr-idtag-icon"
                {...makeSanitizedInnerHtmlProp({ dirtyHtml: idTagIcon })}
              />
            </div>
            <div className={'name-wrapper util-textBold'}>
              <p className="util-noselect">{item.identification_tag}</p>
            </div>
          </div>
        )
      }

      case 'gdpr-driver-id-tag': {
        return (
          <div
            id={uniqueId(`${innerKey}_`)}
            key={uniqueId(`${innerKey}_`)}
            className={`menu-item ${itemClassName} ${
              selected === innerKey ? 'selected' : ''
            }`}
            onClick={() => onSelect(innerKey)}
          >
            <div className={'image-wrapper-id-tag'}>
              <div
                className="gdpr-idtag-icon"
                {...makeSanitizedInnerHtmlProp({ dirtyHtml: idTagIcon })}
              />
            </div>
            <div className={'name-wrapper-id-tags'}>
              <p className="id-tag-label util-noselect">{item.identification_tag}</p>
              <p className="id-tag-sub-label util-noselect">
                <span>
                  <FormattedMessage id="Assign:" />
                </span>
                {_getDriverName(item)}
              </p>
            </div>
            <Switch
              onChange={(checked: boolean) => onChangeTick(checked, item, undefined)}
              checked={
                typeof item.privacy_mode === 'string'
                  ? JSON.parse(item.privacy_mode)
                  : item.privacy_mode
              }
              id={`${innerKey}`}
              checkedIcon={<span className="privacy-switch-label active">ON</span>}
              uncheckedIcon={<span className="privacy-switch-label inactive">OFF</span>}
              height={20}
              onColor={'#B5D55E'}
              offColor={'#999999'}
            />
          </div>
        )
      }

      case 'gdpr-vehicle-total': {
        return (
          <div
            id={uniqueId(`${innerKey}_`)}
            key={uniqueId(`${innerKey}_`)}
            className={`menu-item ${itemClassName} ${
              selected === innerKey || this.isOnMultipleSelection(innerKey)
                ? 'selected'
                : ''
            }`}
            onClick={() => onSelect(innerKey)}
          >
            <div
              className={`gdpr-multiple-selector${
                multipleSelectAvailable &&
                !this.isOnMultipleSelection(innerKey) &&
                innerKey !== selected
                  ? ' active'
                  : ''
              }`}
              onClick={(e) => this.addToMultipleSelection(e, item, keyId)}
            >
              <Icon icon="plus" />
            </div>
            <VehicleIconCircle
              className={`Vehicle-icon gdpr-icon`}
              type={item.vehicle_type}
            />
            <div className={'name-wrapper'}>
              <p className="util-noselect">{item.registration}</p>
            </div>
            <div className="model-wrapper">
              <p className="util-noselect">{item.model}</p>
            </div>
          </div>
        )
      }
      case 'gdpr-vehicle-location': {
        return (
          <div
            id={uniqueId(`${innerKey}_`)}
            key={uniqueId(`${innerKey}_`)}
            className={`menu-item ${itemClassName} ${
              selected === innerKey || this.isOnMultipleSelection(innerKey)
                ? 'selected'
                : ''
            }`}
            onClick={() => onSelect(innerKey)}
          >
            <div
              className={`gdpr-multiple-selector${
                multipleSelectAvailable &&
                !this.isOnMultipleSelection(innerKey) &&
                innerKey !== selected
                  ? ' active'
                  : ''
              }`}
              onClick={(e) => this.addToMultipleSelection(e, item, keyId)}
            >
              <Icon icon="plus" />
            </div>
            <VehicleIconCircle
              className={`Vehicle-icon gdpr-icon`}
              type={item.vehicle_type}
            />
            <div className={'name-wrapper'}>
              <p className="util-noselect">{item.registration}</p>
            </div>
            <div className="model-wrapper">
              <p className="util-noselect">{item.model}</p>
            </div>
            <Switch
              onChange={(checked, event) => onChangeTick(checked, item, event)}
              checked={parseScheduleIdExistence(item.schedule_id)}
              id={`${innerKey}`}
              checkedIcon={<span className="privacy-switch-label active">ON</span>}
              uncheckedIcon={<span className="privacy-switch-label inactive">OFF</span>}
              height={20}
              onColor={'#B5D55E'}
              offColor={'#999999'}
              className={'gdpr-header-square-switch'}
            />
          </div>
        )
      }
      case 'gdpr-vehicle-settings': {
        return (
          <div
            id={uniqueId(`${innerKey}_`)}
            key={uniqueId(`${innerKey}_`)}
            className={`menu-item ${itemClassName} ${
              selected === innerKey ? 'selected' : ''
            }`}
            onClick={() => onSelect(innerKey)}
          >
            <VehicleIconCircle
              className={`Vehicle-icon gdpr-icon`}
              type={item.vehicle_type}
            />
            <div className={'name-wrapper'}>
              <p className="util-noselect">{item.registration}</p>
            </div>
            <div className="model-wrapper">
              <p className="util-noselect">{item.model}</p>
            </div>
          </div>
        )
      }
      default: {
        return null
      }
    }
  }

  onArrowClick = (
    direction: 'left' | 'right',
    scrollCheckId: string | undefined = undefined,
  ) =>
    this.setState({
      visibleIndex: this._offsetMultiplier(direction, scrollCheckId),
    })

  onScroll = (offSet: number) => this.setState({ visibleIndex: offSet })

  render() {
    const { selected, visibleIndex, multipleSelectAvailable } = this.state
    const { itemClassName, keyId, dataList, scrollCheckId } = this.props

    return (
      <div className="gdpr-virtualized-list">
        <div
          className="gdpr-arrow prev"
          id={`gdpr-arrow-prev_${scrollCheckId}`}
        >
          <Icon icon="angle-left" />
        </div>
        <div
          id={scrollCheckId}
          className="gdpr-inner-list-wrapper"
        >
          <VirtualList
            width="100%"
            height={this._tableHeightFromItemClassName(itemClassName)}
            scrollDirection={DIRECTION.HORIZONTAL}
            itemCount={dataList.length}
            itemSize={this._cellWithFromItemClassName(itemClassName)}
            scrollOffset={visibleIndex}
            scrollToAlignment={ALIGNMENT.START}
            onScroll={this.onScroll}
            renderItem={({ index, style }) => (
              <div
                className="gdpr-cell-wrapper"
                style={style}
                key={uniqueId('')}
              >
                {this._getProperCell(
                  dataList[index],
                  selected,
                  itemClassName,
                  dataList[index][keyId],
                  this.onChangeTick,
                  this.onSelect,
                  multipleSelectAvailable,
                  keyId,
                )}
              </div>
            )}
          />
        </div>
        <div
          className="gdpr-arrow next"
          id={`gdpr-arrow-next_${scrollCheckId}`}
        >
          <Icon icon="angle-right" />
        </div>
      </div>
    )
  }
}

export default GDPRHeaderList
