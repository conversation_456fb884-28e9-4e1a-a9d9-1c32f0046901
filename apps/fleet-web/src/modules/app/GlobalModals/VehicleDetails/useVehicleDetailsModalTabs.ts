import { useCallback, useEffect, useMemo, type ComponentType } from 'react'
import { isEmpty, size } from 'lodash'
import { useDispatch } from 'react-redux'
import { useHistory } from 'react-router-dom'

import type { VehicleId } from 'api/types'
import { buildRouteQueryStringKeepingExistingSearchParams } from 'api/utils'
import useVehicleDetailsQuery from 'api/vehicles/useVehicleDetailsQuery'
import { getSettings_UNSAFE } from 'duxs/user'
import {
  getCanImmobiliseVehicles,
  getComputedPrivacyHideLocationsFromDay,
  getHideVehicleRecentActivity,
  getUpdateVehicleOdometerSubuserPermission,
  getVehiclesViewStatus,
} from 'duxs/user-sensitive-selectors'
import {
  fetchImmobiliseVehicleAllowed,
  getImmobiliseVehicleMetaData,
  getVehiclesById,
  mountVehicleDetailsModal,
  unmountVehicleDetailsModal,
} from 'duxs/vehicles'
import type { NavigationTab } from 'src/components/_tabs/NavigationTabs'
import { useTypedSelector } from 'src/redux-hooks'

import { globalVehicleDetailsModalSearchParamsSchema } from '../schema'
import AdvancedSetup from './AdvancedSetup'
import ImmobiliseVehicle from './ImmobiliseVehicle'
import MifleetSettings from './MifleetSettings'
import Odometer from './Odometer'
import RecentActivity from './RecentActivity'
import type { VehicleDetailsModalTab } from './schema'
import Trailers from './Trailers'
import VehicleCustomFields from './VehicleCustomFields'
import VehicleDetailsSection from './VehicleDetailsSection'
import VehicleSettings from './VehicleSettings'

/**
 * Gets the tabs of the vehicle details modal.
 * Also useful if you want the path of a specific tab of the vehicle details modal.
 */
export default function useVehicleDetailsModalTabs(vehicleId: VehicleId) {
  const dispatch = useDispatch()
  const { location } = useHistory()

  const { listTrailers } = useTypedSelector(getSettings_UNSAFE)
  const hideVehicleRecentActivity = useTypedSelector(getHideVehicleRecentActivity)
  const vehiclesViewStatus = useTypedSelector(getVehiclesViewStatus)
  const hasTrailersTab = listTrailers as boolean
  const vehicleDetailsQuery = useVehicleDetailsQuery({ vehicleId })
  const fullVehicleData = useTypedSelector(getVehiclesById).get(vehicleId)
  const immobiliseVehicleMetaData = useTypedSelector(getImmobiliseVehicleMetaData)
  const updateVehicleOdometerSubuserPermission = useTypedSelector(
    getUpdateVehicleOdometerSubuserPermission,
  )
  const privacyHideLocationsFromDay = useTypedSelector(
    getComputedPrivacyHideLocationsFromDay,
  )
  const canImmobiliseVehicles = useTypedSelector(getCanImmobiliseVehicles)

  useEffect(() => {
    dispatch(fetchImmobiliseVehicleAllowed(vehicleId))
    dispatch(mountVehicleDetailsModal({ vehicleId }))
    return () => {
      dispatch(unmountVehicleDetailsModal())
    }
  }, [dispatch, vehicleId])

  const createModalTab = useCallback(
    ({
      tabId,
      label,
      subLabel,
      component = () => null,
      isVisible = true,
      hasSensors,
    }: {
      tabId: VehicleDetailsModalTab
      label: string
      subLabel?: string
      component?: ComponentType<{ vehicleId: VehicleId }>
      isVisible?: boolean
      hasSensors?: boolean
    }) => ({
      id: tabId,
      label,
      subLabel,
      component,
      isVisible,
      path: `${location.pathname}?${buildRouteQueryStringKeepingExistingSearchParams({
        location,
        schema: globalVehicleDetailsModalSearchParamsSchema,
        searchParams: {
          globalModal: {
            modal: 'vehicleDetails',
            params: { vehicleId, tab: tabId, hasSensors },
          },
        },
        options: { shouldJsonStringify: true },
      })}`,
    }),
    [vehicleId, location],
  )

  const searchParams = new URLSearchParams(window.location.search)
  const globalModal = searchParams.get('globalModal')
  const hasSensors = JSON.parse(globalModal || '{}').params.hasSensors

  return useMemo(() => {
    const allTabsObject = {
      RECENT_ACTIVITY: createModalTab({
        tabId: 'RECENT_ACTIVITY',
        label: 'Recent Activity',
        component: RecentActivity,
        isVisible:
          hideVehicleRecentActivity !== true &&
          vehiclesViewStatus &&
          privacyHideLocationsFromDay === 0,
      }),
      DETAILS: createModalTab({
        tabId: 'DETAILS',
        label: 'Vehicle Details',
        component: VehicleDetailsSection,
      }),
      SETTINGS: createModalTab({
        tabId: 'SETTINGS',
        label: 'Vehicle Settings',
        component: VehicleSettings,
      }),
      CUSTOM_FIELDS: createModalTab({
        tabId: 'CUSTOM_FIELDS',
        label: 'Custom Fields',
        component: VehicleCustomFields,
        isVisible:
          vehicleDetailsQuery.status === 'success' &&
          Boolean(
            vehicleDetailsQuery.data.fleetVehicle &&
              size(vehicleDetailsQuery.data.fleetVehicle.customFields),
          ),
      }),
      MIFLEET_SETTINGS: createModalTab({
        tabId: 'MIFLEET_SETTINGS',
        label: 'MiFleet Settings',
        component: MifleetSettings,
        isVisible:
          vehicleDetailsQuery.status === 'success' &&
          vehicleDetailsQuery.data.fleetVehicle.hasMifleet,
      }),
      ODOMETER: createModalTab({
        tabId: 'ODOMETER',
        label: 'Odometer',
        component: Odometer,
        isVisible: updateVehicleOdometerSubuserPermission,
      }),
      TRAILERS: createModalTab({
        tabId: 'TRAILERS',
        label: 'Vehicle Trailers',

        component: Trailers,
        isVisible: hasTrailersTab,
      }),
      IMMOBILISE: createModalTab({
        tabId: 'IMMOBILISE',
        label: 'Immobilise Vehicle',
        component: ImmobiliseVehicle,
        isVisible:
          !isEmpty(immobiliseVehicleMetaData) &&
          fullVehicleData?.startInhibitAllowed &&
          canImmobiliseVehicles,
      }),
      ADVANCED: createModalTab({
        tabId: 'ADVANCED',
        label: 'Advanced Setup',
        component: AdvancedSetup,
        isVisible: Boolean(hasSensors),
        hasSensors: hasSensors,
      }),
    } satisfies Record<VehicleDetailsModalTab, NavigationTab<VehicleDetailsModalTab>>
    const allTabsArray = Object.values(allTabsObject)

    // Filter out non-visible tabs for the object
    const visibleTabsObject = Object.fromEntries(
      Object.entries(allTabsObject).filter(([_key, tab]) => tab.isVisible),
    )

    const visibleTabsArray = Object.values(visibleTabsObject)

    return {
      allTabsObject,
      allTabsArray,
      visibleTabsObject,
      visibleTabsArray,
    }
  }, [
    createModalTab,
    hideVehicleRecentActivity,
    vehiclesViewStatus,
    privacyHideLocationsFromDay,
    vehicleDetailsQuery,
    updateVehicleOdometerSubuserPermission,
    hasTrailersTab,
    immobiliseVehicleMetaData,
    fullVehicleData?.startInhibitAllowed,
    canImmobiliseVehicles,
    hasSensors,
  ])
}
