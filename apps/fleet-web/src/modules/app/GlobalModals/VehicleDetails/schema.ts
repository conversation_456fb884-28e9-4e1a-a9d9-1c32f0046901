import { z } from 'zod'

import { vehicleIdSchema } from 'api/types'

const VEHICLE_DETAILS_MODAL_TABS = [
  'RECENT_ACTIVITY',
  'DETAILS',
  'SETTINGS',
  'CUSTOM_FIELDS',
  'MIFLEET_SETTINGS',
  'ODOMETER',
  'TRAILERS',
  'IMMOBILIS<PERSON>',
  'ADVANCED',
] as const
export const vehicleDetailsModalTabsSchema = z.enum(VEHICLE_DETAILS_MODAL_TABS)
export type VehicleDetailsModalTab = z.infer<typeof vehicleDetailsModalTabsSchema>

export const vehicleDetailsModalSearchParamsSchema = z.object({
  modal: z.literal('vehicleDetails'),
  params: z.object({
    vehicleId: vehicleIdSchema,
    tab: z.any(), // any to allow redirecting to the first available and valid tab
    hasSensors: z.boolean().optional(),
  }),
})
