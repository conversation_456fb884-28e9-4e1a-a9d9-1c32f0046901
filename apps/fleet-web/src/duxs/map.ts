import { fitBounds } from 'google-map-react'
import { match, P } from 'ts-pattern'
import { isNil, kebabCase, toLower, startCase, isEmpty, last, mapValues } from 'lodash'
import { matchPath } from 'react-router'
import type { UnwrapOpaque } from 'type-fest'

import {
  reconciliateGroupItemIds,
  insensitiveCompare,
  checkForValidCoords,
  zoomToBounds,
  MAP_DEFAULT_ZOOM,
  isValidGPSData,
  onMapWithTypePath,
  onFullScreenPath,
  findNwSeFromLatLng,
  toImmutable,
} from 'cartrack-utils'
import { VIEW_MODE } from 'src/util-functions/constants'
import {
  getActiveVehiclesWithDrivers,
  getVehicleGroup,
  getVehicleGroups,
  getFocusedVehicle,
  getTripsSummaryPanelUI,
  RECEIVE_SHARED_VEHICLE_DATA,
  getAvailableVehiclesToShowInMapWithDrivers,
  getVehiclesById,
} from 'duxs/vehicles'
import { CLOSE_DETAILS_PANEL, SELECT_VEHICLE_TRIP_SUMMARY } from 'duxs/shared'
import { receiveComparedTrips } from 'duxs/trip-compare'
import {
  getELDStatus,
  getPreferences,
  getUserPositionAddressStateGetter,
  getStylePositionUnreliableTypeSetting,
} from 'duxs/user-sensitive-selectors'
import { getLandmarks, getMapLandmarks } from 'duxs/landmarks'
import { getGeofences } from 'duxs/geofences-selector'
import {
  receiveTimelineEventsUi,
  receiveTimelineEventsRaw,
  CLEAR_TIMELINE,
  type TimelineEvent,
  fetchMultipleDaysTimeline,
  getIsTimelineBarUILoading,
  getIsTimelineEventsLoading,
  getTimelineEventsByActivityAndMapType,
} from 'duxs/timeline/index'
import { MapTypeId } from 'src/types/extended/google-maps'
import {
  clickedMapContextMenuWhatsNearby,
  clickedMapContextMenuStreetView,
  setIsStreetViewVisible,
  clickedNearbyVehiclesPanelCloseButton,
  clickedLeftPanelPlace,
  clickedLeftPanelCompareTripsButton,
  clickedLeftPanelCompareTripsBackButton,
  refocusVehicle,
  refocusVehicleTrip,
  clickedLeftPanelGeofence,
  clickedLeftPanelLandmarkViewOnMap,
  onLeftPanelVehicleFilterChange,
  onLeftPanelCarpoolVehicleFilterChange,
  setVehicleClusterPanelInitialOpenState,
  clickedLiveVisionRightPanelCloseButton,
  setVehicleClusterPanelClosedState,
  onLeftPanelResetVehicleFilterChange,
  onLeftPanelResetCarpoolVehicleFilter,
  onLeftPanelDriverFilterChange,
  onLeftPanelResetDriverFilterChange,
  clickedClearPlaceSearchButton,
  onMapProviderUIClick,
  onGoogleMapTypeIdChange,
  focusVehicle,
  onLeftPanelSearchBoxClear,
  onVehicleMarkerMouseEnter,
  onVehicleMarkerMouseLeave,
} from 'src/modules/map-view/actions'
import {
  onVehiclesPanelItemPopoverHidden,
  onVehiclesPanelItemPopoverMount,
} from 'src/modules/map-view/FleetMapView/actions'
import { changedActivityDateRange } from 'src/modules/map-view/FleetMapView/DetailsPanel/slice'
import { BookingStatusMapping } from 'src/modules/carpool/utils/constants'
import { locationChangedWithPrevLocation } from './sharedActions'
import { MapApiProvider } from 'api/user/types'
import {
  gcj2wgsIfInChinaMainland,
  gcj2wgsIfInChinaWithoutTaiwan,
  gcj2wgsIfInHongKongOrMacau,
  wgs2gcjIfInChinaMainland,
  wgs2gcjIfInChinaWithoutTaiwan,
  wgs2gcjIfInHongKongOrMacau,
} from 'src/util-functions/china-map-utils'
import { eventStatusClassNameSchema } from 'src/modules/map-view/map/types'
import { MAINTENANCE } from 'src/modules/app/components/routes/maintenance'
import { createSelectorWithStrictMode } from 'src/redux-utils'
import type { AppState } from 'src/root-reducer'
import type { FixMeAny } from 'src/types'
import type { ChangeEventValue } from 'google-map-react'
import type { PlaceDetails } from 'src/shared/google-places'
import type { BasicEvent } from 'api/timeline/types'

import type {
  VehicleWithDriver,
  MapState,
  MapLayers,
  MapVehicleFilterOptions,
  MapCarpoolVehicleFilterOptions,
} from './map-types'
import {
  getDefaultLayerVisibility,
  getMapType,
  getVehicleFocusedLayerVisibility,
} from './map-timeline'
import type { ExcludeStrict } from 'src/types/utils'
import { batchReduxStateUpdatesFromSaga } from 'src/sagas/actions'
import { getHardwareTypes } from './vehicle-sensitive-selectors'

// Actions
const RESET_FOCUSED_ITEM = 'RESET_FOCUSED_ITEM'
const CHANGE_ACTIVE_TIMELINE_TABLE = 'CHANGE_ACTIVE_TIMELINE_TABLE'
const TOGGLE_FOLLOW_FOCUSED_ITEM = 'TOGGLE_FOLLOW_FOCUSED_ITEM'
export const SET_FOLLOW_FOCUSED_ITEM = 'SET_FOLLOW_FOCUSED_ITEM'
const UPDATE_MAP_SIZE = 'UPDATE_MAP_SIZE'
const UPDATE_TIMELINE_SLIDER_PLAYING = 'UPDATE_TIMELINE_SLIDER_PLAYING'
const SET_FOCUSED_POINT = 'SET_FOCUSED_POINT'
const SELECT_LEFT_PANEL_GROUP = 'SELECT_LEFT_PANEL_GROUP'
const PLACES_SERVICE = 'PLACES_SERVICE'

export const CHANGE_MAP_TYPE = 'CHANGE_MAP_TYPE'
export const CHANGE_VIEW_MODE = 'CHANGE_VIEW_MODE'
export const CHANGE_MAP_CENTER_ZOOM = 'CHANGE_MAP_CENTER_ZOOM'
export const JUMP_TO_TIME = 'JUMP_TO_TIME'
export const JUMP_TO_EVENT = 'JUMP_TO_EVENT'
export const SET_LAYER_VISIBILITY = 'SET_LAYER_VISIBILITY'
export const SET_VEHICLE_FOCUSED_LAYER_VISIBILITY =
  'SET_VEHICLE_FOCUSED_LAYER_VISIBILITY'
export const ZOOM_TO_EVENTS = 'ZOOM_TO_EVENTS'
export const UPDATE_LAYER_VISIBILITY = 'UPDATE_LAYER_VISIBILITY'
export const SET_MAP_TYPE_ID = 'SET_MAP_TYPE_ID'
export const SET_MAP_STATE = 'SET_MAP_STATE'
export const ZOOM_AFTER_LOAD = 'ZOOM_AFTER_LOAD'
const SET_CONTEXT_MENU_ELEMENT = 'SET_CONTEXT_MENU_ELEMENT'
export const RESET_ZOOM_FIRST_TIME_LOADING = 'RESET_ZOOM_FIRST_TIME_LOADING'

export const initPlaceDirection = {
  selectedPlace: null as PlaceDetails | null,
}

const mapConfigs = {
  fleet: {
    showFollowVehicle: true,
  },
}

const defaultLayerVisibility: Partial<{ [key in MapLayers]: boolean }> = {
  // Geofences
  geofences: false,
  geofenceLabels: false,
  systemGeofences: false,
  userGeofences: false,
  // Landmarks
  landmarks: false,
  pointsOfInterestLabels: false,
  // Harsh Events
  harshEvents: true,
  harshEventsSpeeding: true,
  // Live Positions
  livePositions: true,
  livePositionLabels: true,
  livePositionClusters: true,
  livePositionShowWhileAVehicleIsSelected: false,
  // Map
  maps: false,
  traffic: false,
  transit: false,
  bicycle: false,
  alerts: false,
  // Trip lines
  showTripLines: false,
}

const defaultFocusedVehicleLayerVisibility = {
  ...defaultLayerVisibility,
  // Live Positions
  livePositions: false,
  livePositionLabels: false,
  // Trip lines
  showTripLines: true,
}

export const initialLayerVisibility = {
  fleet: defaultLayerVisibility,
}

export const initialVehicleFocusedLayerVisibility = {
  fleet: defaultFocusedVehicleLayerVisibility,
}

// Reducer
export const initialState: MapState = {
  visibleRightPanel: null,
  // Map
  center: { lat: 0, lng: 0 },
  zoom: 1,
  layerVisibility: initialLayerVisibility,
  vehicleFocusedLayerVisibility: initialVehicleFocusedLayerVisibility,
  savedMapProps: { lat: 0, lng: 0, zoom: 12 },

  mapState: null,
  type: 'vehicles',
  focusedItemId: null,
  focusedGroupId: '',
  followFocusedItem: false,
  hoveredItem: null,
  hoveredVehicleMarkerId: null,
  selectedTripStartDate: null,
  placesService: null,
  viewMode: VIEW_MODE.portrait,
  prevViewMode: VIEW_MODE.portrait,
  focusedLandmarkOrGeofenceId: null,

  // Street View
  isStreetViewVisible: false,

  // Open Street Maps
  contextMenuElement: null,

  // Left Panel
  visibleLeftPanel: 'default',
  activeFilters: {
    vehicles: {},
    drivers: {},
  },
  carpoolActiveFilters: {
    carpool: {},
  },

  // Place Direction
  ...initPlaceDirection,

  initialLoad: true,
  // Timeline
  timelineProgress: 0,
  activeEventIndex: 0,
  timelineTablesActiveTab: 'all-trips',
  timelineSliderPlaying: false,
  mapTypeId: MapTypeId.ROADMAP,

  focusedPoint: null,
  focusedPointZoom: undefined,
  size: undefined,
  bounds: undefined,

  focusedVehicleLastKnownPosition: null,
}

export default function reducer(state = initialState, action: FixMeAny): MapState {
  console.log('map state', state.zoom)
  if (clickedMapContextMenuWhatsNearby.match(action)) {
    const savedMapProps = {
      center: state.center,
      zoom: state.zoom,
    }

    return {
      ...state,
      visibleLeftPanel: 'nearbyVehicles',
      focusedItemId: null,
      focusedLandmarkOrGeofenceId: null,
      focusedPoint: state.focusedPoint,
      savedMapProps,
    }
  } else if (clickedMapContextMenuStreetView.match(action)) {
    return {
      ...state,
      isStreetViewVisible: true,
    }
  } else if (setIsStreetViewVisible.match(action)) {
    return {
      ...state,
      isStreetViewVisible: action.payload,
    }
  } else if (clickedNearbyVehiclesPanelCloseButton.match(action)) {
    return {
      ...state,
      visibleLeftPanel: state.selectedPlace ? 'places' : 'default',
      focusedPoint: null,
      center: state.savedMapProps.center,
      zoom: state.savedMapProps.zoom,
    }
  } else if (locationChangedWithPrevLocation.match(action)) {
    const { prevLocation, currentRouterState } = action.payload
    let stateToChange: Partial<MapState> = {}

    if (prevLocation !== undefined) {
      if (
        matchPath(prevLocation.pathname, {
          path: currentRouterState.location.pathname,
          exact: true,
        }) !== null
      ) {
        return state
      }

      // Reset map left panel state if previous page was in /map/:type
      if (onMapWithTypePath(prevLocation.pathname)) {
        stateToChange = {
          visibleLeftPanel: initialState.visibleLeftPanel,
          selectedPlace: initialState.selectedPlace, // For Places left panel
          focusedPoint: initialState.focusedPoint, // For What's Nearby left panel
          // Reset focused item to close details panel whenever we change map type (fleet, svr, asset trackers).
          // This can be removed whenever timeline events are loaded properly when switching map type to allow the focused vehicle to maintain selected across map types
          // keep map state when change it to full screen mode and exit full screen mode
          ...(!onFullScreenPath(currentRouterState?.location?.pathname) &&
          !onFullScreenPath(prevLocation.pathname)
            ? getResetFocusedItemState()
            : {}),
        }
      }
    }

    return {
      ...state,
      ...stateToChange,
    }
  } else if (clickedLeftPanelPlace.match(action)) {
    return {
      ...state,
      selectedPlace: action.payload,
      visibleLeftPanel: 'places',
    }
  } else if (clickedLeftPanelCompareTripsButton.match(action)) {
    return {
      ...state,
      visibleLeftPanel: 'compareTrips',
    }
  } else if (clickedLeftPanelCompareTripsBackButton.match(action)) {
    return {
      ...state,
      visibleLeftPanel: 'default',
    }
  } else if (refocusVehicle.match(action)) {
    return focusMapInBoundary(action.payload, state)
  } else if (refocusVehicleTrip.match(action)) {
    return focusMapInBoundary(action.payload, state)
  } else if (onVehiclesPanelItemPopoverMount.match(action)) {
    return { ...state, hoveredItem: action.payload.vehicleId }
  } else if (onVehiclesPanelItemPopoverHidden.match(action)) {
    /* Since the popover being hidden may not be the one current being mounted (the order is not guaranteed)
       we have to check it first before setting nullifying the current hovered item
      */
    if (action.payload.vehicleId === state.hoveredItem) {
      return { ...state, hoveredItem: null }
    }

    return state
  } else if (receiveTimelineEventsRaw.match(action)) {
    const { payload } = action

    if (payload.timelineEventsRaw.length === 0 || state.mapState === null) {
      return state
    }
    const activeEventIndex = payload.timelineEventsRaw.length - 1
    const defaultState = {
      ...state,
      timelineProgress: 1,
      activeEventIndex,
      center: state.mapState.center,
      zoom: state.mapState.zoom,
    }
    const lastEvent = last(payload.timelineEventsRaw)

    if (isNil(lastEvent)) {
      return defaultState
    }

    /**
     * Zoom to bounds on events received from initial request (not polling).
     * If events were received by polling, only zoom to bounds if the timeline slider is not playing and a vehicle is being followed.
     */
    const shouldZoomToBounds = (() => {
      if (payload.isPollingEvents) {
        return !state.timelineSliderPlaying && state.followFocusedItem
      }

      return true // Initial request
    })()

    if (shouldZoomToBounds) {
      /**
       Only consider last event for the trip if current day is selected and the event has valid coordinates.
       This way, the map will be centered on the last vehicle position
       while on the previous days it will be centered around the whole trip
       */
      const boundaryEvents =
        payload.isTodayDateSelected && isValidGPSData(lastEvent.lat, lastEvent.lng)
          ? [lastEvent]
          : payload.timelineEventsRaw

      const [lat, lng, zoom] = zoomToBounds(boundaryEvents, state.size, {
        maxZoom: MAP_DEFAULT_ZOOM,
      })

      if (!isValidGPSData(lat, lng)) {
        return {
          ...defaultState,
          ...(state.focusedVehicleLastKnownPosition !== null
            ? { center: state.focusedVehicleLastKnownPosition }
            : {}),
        }
      } else {
        // Don't change map zoom if vehicle is being followed
        return {
          ...defaultState,
          center: { lat, lng },
          ...(state.followFocusedItem ? {} : { zoom }),
        }
      }
    }

    // Return state with latest event updated if timeline slider in the end. This will update the focused vehicle position and the active event
    if (state.timelineProgress === 1) {
      return defaultState
    }

    return state
  } else if (fetchMultipleDaysTimeline.succeeded.match(action)) {
    const stateCenteredInBoundary = focusMapInBoundary(
      action.payload.events.allIds.reduce<
        Array<ExcludeStrict<BasicEvent['coords'], null>>
      >((acc, eventId) => {
        const eventCoords = action.payload.events.byId[eventId].coords

        if (eventCoords !== null) {
          acc.push(eventCoords)
        }

        return acc
      }, []),
      state,
    )

    const lastEventIndex =
      action.payload.events.allIds.length > 0
        ? action.payload.events.allIds.length - 1
        : null

    const lastEvent =
      lastEventIndex !== null
        ? action.payload.events.byId[action.payload.events.allIds[lastEventIndex]]
        : null

    return {
      ...stateCenteredInBoundary,
      timelineProgress: 1,
      activeEventIndex: lastEventIndex,
      focusedVehicleLastKnownPosition:
        lastEventIndex === null || lastEvent?.coords === null
          ? action.payload.lastPosition
          : null,
    }
  } else if (changedActivityDateRange.match(action)) {
    return {
      ...state,
      selectedTripStartDate: action.payload.activityDateRange.to,
    }
  } else if (clickedLeftPanelGeofence.match(action)) {
    const { geofence } = action.payload
    const { seLat, seLng, nwLat, nwLng } = findNwSeFromLatLng(geofence.geometry)

    const bounds = {
      nw: { lat: nwLat, lng: nwLng },
      se: { lat: seLat, lng: seLng },
      ne: { lat: 0, lng: 0 },
      sw: { lat: 0, lng: 0 },
    }

    const { width, height } = state.size
    const { zoom, newBounds } = fitBounds(bounds, { width, height })

    return {
      ...state,
      center: {
        lat: (newBounds.nw.lat + newBounds.se.lat) / 2,
        lng: (newBounds.nw.lng + newBounds.se.lng) / 2,
      },
      zoom,
      focusedLandmarkOrGeofenceId: { type: 'geofence', id: geofence.id },
    }
  } else if (clickedLeftPanelLandmarkViewOnMap.match(action)) {
    const { landmark } = action.payload

    return {
      ...state,
      center: {
        lat: landmark.lat,
        lng: landmark.lng,
      },
      focusedLandmarkOrGeofenceId: { type: 'landmark', id: landmark.id },
      zoom: 16,
    }
  } else if (receiveTimelineEventsUi.match(action)) {
    const lastPosition = action.payload.lastPosition

    // Zoom to last known position if there is no trips for selected day
    if (action.payload.trips === undefined) {
      if (!isValidGPSData(lastPosition.lat, lastPosition.lng)) {
        return state
      }

      const shouldFollowVehicle =
        !state.timelineSliderPlaying && state.followFocusedItem

      if (shouldFollowVehicle) {
        return {
          ...state,
          center: { lat: lastPosition.lat, lng: lastPosition.lng },
          focusedVehicleLastKnownPosition: lastPosition,
        }
      } else {
        return {
          ...state,
          focusedVehicleLastKnownPosition: lastPosition,
        }
      }
    }

    if (isValidGPSData(lastPosition.lat, lastPosition.lng)) {
      return {
        ...state,
        focusedVehicleLastKnownPosition: lastPosition,
      }
    }

    return state
  } else if (onLeftPanelVehicleFilterChange.match(action)) {
    const { filterChecked, filterName } = action.payload

    const newVehicleFilters = {
      ...state.activeFilters.vehicles,
    }

    if (filterChecked) {
      newVehicleFilters[filterName] = true
    } else {
      delete newVehicleFilters[filterName]
    }

    return {
      ...state,
      activeFilters: { ...state.activeFilters, vehicles: newVehicleFilters },
    }
  } else if (onLeftPanelResetVehicleFilterChange.match(action)) {
    return {
      ...state,
      activeFilters: { ...state.activeFilters, vehicles: {} },
    }
  } else if (onLeftPanelDriverFilterChange.match(action)) {
    const { filterName } = action.payload

    return {
      ...state,
      activeFilters: { ...state.activeFilters, drivers: { [filterName]: true } },
    }
  } else if (onLeftPanelResetDriverFilterChange.match(action)) {
    return {
      ...state,
      activeFilters: { ...state.activeFilters, drivers: {} },
    }
  } else if (onLeftPanelCarpoolVehicleFilterChange.match(action)) {
    const { filterChecked, filterName } = action.payload

    const newVehicleFilters = {
      ...state.carpoolActiveFilters.carpool,
    }

    if (filterChecked) {
      newVehicleFilters[filterName] = true
    } else {
      delete newVehicleFilters[filterName]
    }

    return {
      ...state,
      carpoolActiveFilters: {
        ...state.carpoolActiveFilters,
        carpool: newVehicleFilters,
      },
    }
  } else if (onLeftPanelResetCarpoolVehicleFilter.match(action)) {
    return {
      ...state,
      carpoolActiveFilters: {
        ...state.carpoolActiveFilters,
        carpool: {},
      },
    }
  } else if (setVehicleClusterPanelInitialOpenState.match(action)) {
    return {
      ...state,
      visibleRightPanel: 'vehicle-clusters',
    }
  } else if (clickedLiveVisionRightPanelCloseButton.match(action)) {
    return {
      ...state,
      visibleRightPanel: null,
    }
  } else if (setVehicleClusterPanelClosedState.match(action)) {
    return {
      ...state,
      visibleRightPanel: null,
    }
  } else if (clickedClearPlaceSearchButton.match(action)) {
    return {
      ...state,
      focusedPoint: null,
      selectedPlace: null,
      focusedPointZoom: undefined,
      visibleLeftPanel: 'default',
    }
  } else if (onMapProviderUIClick.match(action)) {
    const { clickedMapProvider, currentMapProvider, mapTypeId } = action.payload

    if (clickedMapProvider === currentMapProvider) {
      return state
    }

    return (
      match({
        currentMapProvider: currentMapProvider as UnwrapOpaque<
          typeof currentMapProvider
        >,
        clickedMapProvider: clickedMapProvider as UnwrapOpaque<
          typeof clickedMapProvider
        >,
      })
        // NOTE: if google -> autonavi,
        // if roadmap, should wgs -> gcj when in HK/MC
        // else, should wgs -> gcj when in China without Taiwan
        .with(
          {
            currentMapProvider: MapApiProvider.GOOGLE,
            clickedMapProvider: MapApiProvider.AUTO_NAVI_CHINESE,
          },
          () => ({
            ...state,
            center:
              mapTypeId === MapTypeId.ROADMAP
                ? wgs2gcjIfInHongKongOrMacau(state.center)
                : wgs2gcjIfInChinaWithoutTaiwan(state.center),
          }),
        )
        // NOTE: if autonavi -> google,
        // if roadmap, should  when in HK/MC
        // else, should gcj -> wgs when in China without Taiwan
        .with(
          {
            currentMapProvider: MapApiProvider.AUTO_NAVI_CHINESE,
            clickedMapProvider: MapApiProvider.GOOGLE,
          },
          () => ({
            ...state,
            center:
              mapTypeId === MapTypeId.ROADMAP
                ? gcj2wgsIfInHongKongOrMacau({
                    gcjLat: state.center.lat,
                    gcjLng: state.center.lng,
                  })
                : gcj2wgsIfInChinaWithoutTaiwan({
                    gcjLat: state.center.lat,
                    gcjLng: state.center.lng,
                  }),
          }),
        )
        .otherwise(() => state)
    )
  } else if (onGoogleMapTypeIdChange.match(action)) {
    const { selectedMapTypeId, currentMapTypeId } = action.payload
    if (selectedMapTypeId === currentMapTypeId) {
      return state
    }

    if (selectedMapTypeId === MapTypeId.ROADMAP) {
      return {
        ...state,
        center: wgs2gcjIfInChinaMainland(state.center),
        mapTypeId: selectedMapTypeId,
      }
    }

    if (currentMapTypeId === MapTypeId.ROADMAP) {
      return {
        ...state,
        mapTypeId: selectedMapTypeId,
        center: gcj2wgsIfInChinaMainland({
          gcjLat: state.center.lat,
          gcjLng: state.center.lng,
        }),
      }
    }

    return {
      ...state,
      mapTypeId: selectedMapTypeId,
    }
  } else if (batchReduxStateUpdatesFromSaga.match(action)) {
    console.log('batchReduxStateUpdatesFromSaga', action.payload.mapState)
    return action.payload.mapState ? { ...state, ...action.payload.mapState } : state
  } else if (onLeftPanelSearchBoxClear.match(action)) {
    return {
      ...state,
      focusedLandmarkOrGeofenceId: null,
    }
  } else if (onVehicleMarkerMouseEnter.match(action)) {
    return { ...state, hoveredVehicleMarkerId: action.payload.vehicleId }
  } else if (onVehicleMarkerMouseLeave.match(action)) {
    return {
      ...state,
      hoveredVehicleMarkerId:
        state.hoveredVehicleMarkerId === action.payload.vehicleId
          ? // Only reset if the hovered vehicle is the one that is being unhovered.
            // This is to prevent the hover state from being reset when the user hovers over another vehicle quickly.
            null
          : state.hoveredVehicleMarkerId,
    }
  }

  switch (action.type) {
    case CHANGE_VIEW_MODE: {
      return {
        ...state,
        prevViewMode: state.viewMode,
        viewMode: action.payload,
      }
    }
    case RESET_FOCUSED_ITEM: {
      return {
        ...state,
        ...getResetFocusedItemState(),
      }
    }
    case CHANGE_MAP_TYPE: {
      return { ...state, type: action.payload.type }
    }
    case TOGGLE_FOLLOW_FOCUSED_ITEM: {
      return { ...state, followFocusedItem: !state.followFocusedItem }
    }
    case SET_FOLLOW_FOCUSED_ITEM: {
      return { ...state, followFocusedItem: action.payload }
    }
    case ZOOM_AFTER_LOAD: {
      return { ...state, initialLoad: false }
    }
    case RECEIVE_SHARED_VEHICLE_DATA: {
      return {
        ...state,
        ...focusMapItem(
          {
            item: action.payload.vehicle,
          },
          state,
        ),
        focusedItemId: action.payload.vehicle.id,
        focusedLandmarkOrGeofenceId: null,
        timelineProgress: 1,
        selectedTripStartDate: action.payload.dateTimeRange?.to,
      }
    }

    case SELECT_VEHICLE_TRIP_SUMMARY: {
      return {
        ...state,
        selectedTripStartDate: action.payload.startDate,
        activeEventIndex: null,
        timelineProgress: 1,
      }
    }
    case CLOSE_DETAILS_PANEL: {
      return {
        ...state,
        ...getResetFocusedItemState(),
        center: { lat: state.savedMapProps.lat, lng: state.savedMapProps.lng },
        zoom: state.savedMapProps.zoom,
        timelineProgress: 0,
        followFocusedItem: false,
        focusedVehicleLastKnownPosition: null,
      }
    }
    case CHANGE_MAP_CENTER_ZOOM: {
      console.trace('CHANGE_MAP_CENTER_ZOOM', action.payload.zoom)

      if (isValidGPSData(action.payload.center.lat, action.payload.center.lng)) {
        return {
          ...state,
          center: action.payload.center,
          zoom: action.payload.zoom || state.zoom,
        }
      }

      return state
    }
    case JUMP_TO_TIME: {
      const {
        nextProgress: timelineProgress,
        nextActiveEventIndex: activeEventIndex,
        nextCoords,
      } = action.payload

      const center =
        nextCoords !== null && isValidGPSData(nextCoords.lat, nextCoords.lng)
          ? {
              lat: nextCoords.lat,
              lng: nextCoords.lng,
            }
          : state.center

      return {
        ...state,
        center,
        timelineProgress,
        activeEventIndex,
      }
    }

    case UPDATE_TIMELINE_SLIDER_PLAYING: {
      return { ...state, timelineSliderPlaying: action.payload }
    }
    case SET_FOCUSED_POINT: {
      return {
        ...state,
        focusedPoint: action.payload.point as ReturnType<
          typeof setFocusedPoint
        >['payload']['point'],
        focusedPointZoom: state.zoom,
      }
    }

    case SET_VEHICLE_FOCUSED_LAYER_VISIBILITY: {
      return {
        ...state,
        vehicleFocusedLayerVisibility: {
          ...state.vehicleFocusedLayerVisibility,
          ...action.payload.layers,
        },
      }
    }
    case PLACES_SERVICE: {
      return {
        ...state,
        placesService: action.payload,
      }
    }
    case UPDATE_LAYER_VISIBILITY: {
      return {
        ...state,
        layerVisibility: action.payload,
      }
    }

    case CHANGE_ACTIVE_TIMELINE_TABLE: {
      return {
        ...state,
        timelineTablesActiveTab:
          state.timelineTablesActiveTab === action.payload.tab
            ? 'all-trips'
            : action.payload.tab,
      }
    }

    case SELECT_LEFT_PANEL_GROUP: {
      return {
        ...state,
        focusedGroupId: action.payload.id,
      }
    }

    case receiveComparedTrips.type: {
      return {
        ...state,
        activeEventIndex: 0,
        timelineProgress: 0,
      }
    }
    case UPDATE_MAP_SIZE: {
      return {
        ...state,
        size: action.payload.size,
        bounds: action.payload.bounds,
      }
    }
    case ZOOM_TO_EVENTS: {
      const defaultState = {
        ...state,
        center: {
          lat: state.savedMapProps.lat,
          lng: state.savedMapProps.lng,
        },
        zoom: state.savedMapProps.zoom,
      }

      if (isEmpty(action.payload.events)) {
        return defaultState
      }

      const [lat, lng, zoom] = zoomToBounds(action.payload.events, state.size, {
        maxZoom: action.payload.maxZoom,
      })

      if (!isValidGPSData(lat, lng)) {
        return defaultState
      } else {
        return {
          ...state,
          center: { lat, lng },
          zoom,
        }
      }
    }

    case CLEAR_TIMELINE: {
      return {
        ...state,
        activeEventIndex: null,
        timelineProgress: 0,
      }
    }
    case SET_MAP_TYPE_ID: {
      return {
        ...state,
        mapTypeId: action.payload.type,
      }
    }
    case SET_MAP_STATE: {
      return {
        ...state,
        mapState: action.payload.mapState,
      }
    }
    case SET_CONTEXT_MENU_ELEMENT: {
      return {
        ...state,
        contextMenuElement: action.data,
      }
    }

    default: {
      return { ...state }
    }
  }
}

// Utils
export const focusMapItem = (
  payload: {
    item: { latitude: number; longitude: number }
  },
  state: MapState,
) => {
  const { item } = payload
  const { hasValidCoords, lat, lng } = checkForValidCoords(item)

  const center = hasValidCoords ? { lat: Number(lat), lng: Number(lng) } : state.center
  const zoom =
    hasValidCoords && state.zoom < MAP_DEFAULT_ZOOM ? MAP_DEFAULT_ZOOM : state.zoom
  const savedMapProps = state.focusedItemId
    ? state.savedMapProps
    : {
        ...state.center,
        zoom: state.zoom,
      }

  return { center, zoom, savedMapProps }
}

const focusMapInBoundary = (
  itemBounds: Array<TimelineEvent> | Array<ExcludeStrict<BasicEvent['coords'], null>>,
  state: MapState,
) => {
  if (state.mapState === null) {
    return state
  }

  const defaultState = {
    ...state,
    center: state.mapState.center,
    zoom: state.mapState.zoom,
  }

  if (isNil(itemBounds)) {
    return defaultState
  }

  const [lat, lng, zoom] = zoomToBounds(itemBounds, state.size, {
    maxZoom: MAP_DEFAULT_ZOOM,
    shortLatLng: true,
  })

  if (!isValidGPSData(lat, lng)) {
    return defaultState
  } else {
    return {
      ...state,
      center: { lat, lng },
      zoom,
    }
  }
}

const getResetFocusedItemState = () =>
  ({
    focusedItemId: null,
    activeEventIndex: null,
    focusedLandmarkOrGeofenceId: null,
  }) satisfies Partial<MapState>

// Action Creators

export function changeViewMode(newViewMode: MapState['viewMode']) {
  return {
    type: CHANGE_VIEW_MODE,
    payload: newViewMode,
  }
}

export function changeType(type: FixMeAny) {
  return {
    type: CHANGE_MAP_TYPE,
    payload: { type },
  }
}

export function setFollowFocusedItem(followFocusedItem: MapState['followFocusedItem']) {
  return {
    type: SET_FOLLOW_FOCUSED_ITEM,
    payload: followFocusedItem,
  }
}
export function toggleFollowFocusedItem() {
  return {
    type: TOGGLE_FOLLOW_FOCUSED_ITEM,
  }
}

export function closeDetailsPanel() {
  return {
    type: CLOSE_DETAILS_PANEL,
  }
}

export function selectTripSummary(
  type: 'vehicle',
  item: Record<string, any>,
  startDate: Date,
) {
  return {
    type: SELECT_VEHICLE_TRIP_SUMMARY,
    payload: { [type]: item, startDate },
  }
}

/**
 * Should only be used in map pages
 */
export function focusItem(
  _type: 'vehicle',
  item: FixMeAny,
  dateTimeRange: { from: Date; to: Date } | 'default',
) {
  return focusVehicle({ vehicle: item, dateTimeRange })
}

/**
 * @deprecated
 * This action creator is used as a setter for everything and it's hard to know where it comes from (even with "origin").
 * Please, prefer creating an action creator for your handler and making the change of zoom or center values explicit in the reducer for that action.
 * As a reference - https://redux.js.org/style-guide/style-guide#model-actions-as-events-not-setters
 */
export function changeMapCenterZoom(
  lat: number | string,
  lng: number | string,
  zoom?: number,
  origin?: string,
) {
  return {
    type: CHANGE_MAP_CENTER_ZOOM,
    payload: { center: { lat: Number(lat), lng: Number(lng) }, zoom },
    meta: { origin },
  }
}

export function updateTimelineSliderPlaying(status: FixMeAny) {
  return {
    type: UPDATE_TIMELINE_SLIDER_PLAYING,
    payload: status,
  }
}

export function jumpToTime({
  nextProgress,
  nextActiveEventIndex,
  nextCoords,
}: {
  nextProgress: MapState['timelineProgress']
  nextActiveEventIndex: MapState['activeEventIndex']
  nextCoords: { lat: number; lng: number } | null
}) {
  return {
    type: JUMP_TO_TIME,
    payload: { nextProgress, nextActiveEventIndex, nextCoords },
  }
}

/**
 * Should only be used outside of map pages because this does an history.push to the map and will fetch timeline events.
 * If you want to jump to an event in a map page, use `jumpToTime` instead.
 */
export function jumpToEvent(eventTs: Date, vehicleId: FixMeAny) {
  return {
    type: JUMP_TO_EVENT,
    payload: { eventTs, vehicleId },
  }
}

export function setFocusedPoint(point: {
  lat: number | `${number}`
  lng: number | `${number}`
}) {
  return {
    type: SET_FOCUSED_POINT,
    payload: { point: { lat: Number(point.lat), lng: Number(point.lng) } },
  }
}

export function setPlacesService(service: MapState['placesService']) {
  return {
    type: PLACES_SERVICE,
    payload: service,
  }
}

export function updateLayerVisibility(
  updatedLayerVisibility: Partial<{
    [key in MapLayers]: boolean | Record<string, boolean | undefined>
  }>,
) {
  return {
    type: UPDATE_LAYER_VISIBILITY,
    payload: updatedLayerVisibility,
  }
}

export function setLayerVisibility(
  layers: Partial<{ [key in MapLayers]: boolean }>,
  details: {
    layerGetter: (state: AppState) => Record<string, any>
    layerSetter: typeof updateLayerVisibility
  } = {
    layerGetter: getDefaultLayerVisibility,
    layerSetter: updateLayerVisibility,
  },
) {
  return {
    type: SET_LAYER_VISIBILITY,
    payload: { layers, details },
  }
}

export function setVehicleFocusedLayerVisibility(
  layers: Partial<{ [key in MapLayers]: boolean }>,
) {
  return setLayerVisibility(layers, {
    layerGetter: getVehicleFocusedLayerVisibility,
    layerSetter: (updatedLayerVisibility) => ({
      type: SET_VEHICLE_FOCUSED_LAYER_VISIBILITY,
      payload: updatedLayerVisibility,
    }),
  })
}

export function changeTimelineTablesActiveTab(tab: FixMeAny) {
  return {
    type: CHANGE_ACTIVE_TIMELINE_TABLE,
    payload: { tab },
  }
}

export function focusGroup(id: FixMeAny) {
  return {
    type: SELECT_LEFT_PANEL_GROUP,
    payload: { id },
  }
}

export function updateMapSize(size: FixMeAny, bounds: FixMeAny) {
  return {
    type: UPDATE_MAP_SIZE,
    payload: { size, bounds },
  }
}

export function zoomToEvents(events: Array<Record<string, unknown>>, maxZoom: number) {
  return {
    type: ZOOM_TO_EVENTS,
    payload: { events, maxZoom },
  }
}

export function setNonGoogleMapTypeId(type: google.maps.MapTypeId) {
  return {
    type: SET_MAP_TYPE_ID,
    payload: { type },
  }
}

export function setMapState(mapState: ChangeEventValue) {
  return {
    type: SET_MAP_STATE,
    payload: { mapState },
  }
}

export function resetFocusedItem() {
  return {
    type: RESET_FOCUSED_ITEM,
  }
}

export function resetZoomFirstTimeLoading() {
  return {
    type: RESET_ZOOM_FIRST_TIME_LOADING,
  }
}

export function setContextMenuElement(elementType: MapState['contextMenuElement']) {
  return {
    type: SET_CONTEXT_MENU_ELEMENT,
    data: elementType,
  }
}

// Map
export const getCenter = (state: AppState) => state.map.center
export const getZoom = (state: AppState) => state.map.zoom
export const getFocusedPoint = (state: AppState) => state.map.focusedPoint
export const getMapState = (state: AppState) => state.map.mapState

/* Start Map Layers selectors */

/* End Map Layers selectors */

export const getPlacesService = (state: AppState) => state.map.placesService
export const getHoveredItem = (state: AppState) => state.map.hoveredItem
export const getHoveredVehicleMarkerId = (state: AppState) =>
  state.map.hoveredVehicleMarkerId
export const getHoveredVehicleMarkerData = createSelectorWithStrictMode(
  getVehiclesById,
  getHoveredVehicleMarkerId,
  (vehiclesById, hoveredVehicleId) => {
    if (!hoveredVehicleId) {
      return null
    }
    const rawVehicle = vehiclesById.get(hoveredVehicleId)
    if (!rawVehicle) {
      return null
    }
    const { positionDescription, ...vehicle } = rawVehicle
    return {
      ...vehicle,
      position: positionDescription,
    }
  },
)
export const getFollowFocusedItem = (state: AppState) => state.map.followFocusedItem
export const getFocusedPointZoom = (state: AppState) => state.map.focusedPointZoom
export const getFocusedGroupId = (state: AppState) => state.map.focusedGroupId
export const getFocusedLandmarkId = (state: AppState) => {
  if (
    state.map.focusedLandmarkOrGeofenceId &&
    state.map.focusedLandmarkOrGeofenceId.type === 'landmark'
  ) {
    return state.map.focusedLandmarkOrGeofenceId.id
  }
  return null
}
export const getFocusedGeofenceId = (state: AppState) => {
  if (
    state.map.focusedLandmarkOrGeofenceId &&
    state.map.focusedLandmarkOrGeofenceId.type === 'geofence'
  ) {
    return state.map.focusedLandmarkOrGeofenceId.id
  }
  return null
}

export const getMapSize = (state: AppState) => state.map.size
export const getMapBounds = (state: AppState) => state.map.bounds
export const getSelectedPlace = (state: AppState) => state.map.selectedPlace
export const getViewMode = (state: AppState) => {
  const userPreferences = getPreferences(state)
  return userPreferences && userPreferences.viewMode !== undefined
    ? userPreferences.viewMode
    : state.map.viewMode
}

export const getPrevViewMode = (state: AppState) => state.map.prevViewMode

// Street View

export const getIsStreetViewVisible = (state: AppState) => state.map.isStreetViewVisible

// Map-View
export const getMapConfigs = (_state: AppState) => mapConfigs.fleet

export const getIsMapTypeFleetTab = (state: AppState) => {
  const type = getMapType(state)
  return type === 'vehicles' || type === 'fleet'
}

export const getTypeSortMethod = createSelectorWithStrictMode(
  getPreferences,
  getMapType,
  (preferences, type) =>
    isNil(preferences.mapViewSortMethods[type])
      ? preferences.mapViewSortMethods.default
      : preferences.mapViewSortMethods[type],
)

export const getSelectedHardwareType = (state: AppState) => {
  const types = getHardwareTypes(state)
  const type = toLower(startCase(getMapType(state)))
  return types.find((el) => toLower(el) === type)
}

export const getMapSubTabs = (state: AppState) => {
  const hardwareTypes = getHardwareTypes(state)
  const isFleetExist = (hardwareTypes || []).find(
    (item) => item.toLowerCase() === 'fleet',
  )
  let reOrderHardwareTypes = hardwareTypes.filter(
    (item) => item.toLowerCase() !== 'fleet',
  )
  if (isFleetExist) {
    reOrderHardwareTypes = ['Fleet', ...reOrderHardwareTypes]
  }
  return getELDStatus(state)
    ? [...reOrderHardwareTypes, 'Drivers']
    : reOrderHardwareTypes
}

export const getHardwareFromType = createSelectorWithStrictMode(
  getMapType,
  (state: AppState) => state.vehicles.hardwareTypes,
  (type, types) => {
    if (type === 'vehicles') return 'Fleet'
    if (type === 'point-units' || type === 'point units') return 'Point Units'
    return types.find((t) => kebabCase(t) === type)
  },
)

export const getFocusedVehicleGroupId = (state: AppState) => getFocusedGroupId(state)

// Left Panel
export const getSelectedTripStartDate = (state: AppState) =>
  state.map.selectedTripStartDate

const getGroups = (state: AppState) => getVehicleGroups(state)

export const getActiveFilters = (state: AppState) => state.map.activeFilters

export const getCarpoolActiveFilters = (state: AppState) =>
  state.map.carpoolActiveFilters

// See https://cartrack.atlassian.net/browse/FTW-8106?focusedCommentId=1084581 for more info
// Using a Set for faster lookup performance in filterFn
const statusAggregatorMap = {
  driving: new Set([
    'stationary',
    'driving',
    'driving-manual',
    'harsh-accel',
    'harsh-acceleration',
    'harsh-braking',
    'harsh-turning',
    'ignition-on',
    'max-speed',
    'speeding',
    'excessive-rpm',
  ]),
  idling: new Set(['idling', 'excessive-idling']),
  ignitionOff: new Set(['ignition-off']),
  noSignal: new Set(['no-signal', 'no-signal ns-with-time']),
  maintenance: new Set(['maintenance']),
} satisfies Readonly<Record<string, ReadonlySet<VehicleWithDriver['statusClassName']>>>

export const getDriversFilters = createSelectorWithStrictMode(
  getActiveFilters,
  (activeFilters) => {
    const activeDriverFilters = activeFilters.drivers

    const driverFilterOptions = {
      assignDriver: {
        label: 'Driver Assigned',
      },
      noAssignDriver: {
        label: 'No Driver Assigned',
      },
    }

    return {
      filterOptions: driverFilterOptions,
      filters: activeDriverFilters,
    }
  },
)

export const getVehiclesFilters = createSelectorWithStrictMode(
  getActiveFilters,
  getUserPositionAddressStateGetter,
  MAINTENANCE.MAIN.selector,
  (activeFilters, getUserPositionAddressState, listMaintenanceSetting) => {
    const activeVehicleFilters = activeFilters.vehicles

    const vehicleFilterOptions: MapVehicleFilterOptions = {
      driving: {
        filterFn: ({ statusClassName }: VehicleWithDriver) =>
          statusAggregatorMap.driving.has(statusClassName as FixMeAny),
        icon: 'circle',
        label: 'Driving',
        className: 'util-textDriving',
      },
      idling: {
        filterFn: ({ statusClassName }: VehicleWithDriver) =>
          statusAggregatorMap.idling.has(statusClassName as FixMeAny),
        icon: 'circle',
        label: 'Idling',
        className: 'util-textIdling',
      },
      'moving-ignition-off': {
        filterFn: ({ statusClassName }: VehicleWithDriver) =>
          statusAggregatorMap.idling.has(statusClassName as FixMeAny),
        icon: 'circle',
        label: 'Moving - Ignition Off',
        className: 'util-textMovingIgnitionOff',
      },
      'ignition-off': {
        filterFn: ({ statusClassName }: VehicleWithDriver) =>
          statusAggregatorMap.ignitionOff.has(statusClassName as FixMeAny) ||
          /* statuses that are not recognized will be parsed as ignition-off */
          !eventStatusClassNameSchema.has(statusClassName as FixMeAny),
        icon: 'circle',
        label: 'Ignition Off',
        className: 'util-textMuted',
      },
      'no-signal': {
        filterFn: ({ statusClassName }: VehicleWithDriver) =>
          statusAggregatorMap.noSignal.has(statusClassName as FixMeAny),
        icon: 'signal-slash',
        label: 'No Signal',
        className: 'util-textMuted',
      },
      driver: {
        property: 'driver',
        icon: 'user',
        label: 'Driver Assigned',
        className: 'util-textMuted',
      },
      noDriver: {
        property: 'driver',
        icon: 'user-times',
        label: 'No Driver Assigned',
        className: 'util-textMuted',
      },
      noGPS: {
        filterFn: ({ gpsFixType, positionDescription }: VehicleWithDriver) =>
          match(
            getUserPositionAddressState({
              gpsFixType,
              address: positionDescription,
            }),
          )
            .with({ visibility: 'PUBLIC', type: 'noGPS' }, () => true)
            .otherwise(() => false),
        icon: 'map-marker-alt',
        label: 'No GPS',
        className: 'util-textMuted',
      },
      maintenance: listMaintenanceSetting
        ? {
            filterFn: ({ statusClassName }: VehicleWithDriver) =>
              statusAggregatorMap.maintenance.has(statusClassName as FixMeAny),
            icon: 'tools',
            label: 'Under Maintenance',
            className: 'util-textMaintenance',
          }
        : 'not_used',
      lostVisibility: {
        filterFn: ({ wasActiveInLastXDays }: VehicleWithDriver) =>
          !wasActiveInLastXDays,
        icon: 'eye-slash',
        label: 'Lost Visibility',
        className: 'util-textMuted',
      },
    }

    return {
      filters: mapValues(
        activeVehicleFilters,
        (
          _filterValue,
          untypedFilterKey,
        ):
          | { property: keyof VehicleWithDriver }
          | { filterFn: (v: VehicleWithDriver) => boolean }
          | 'not_used' => {
          const filterKey = untypedFilterKey as keyof typeof activeVehicleFilters
          const filterToApply = vehicleFilterOptions[filterKey]

          if (filterToApply === 'not_used') {
            return 'not_used'
          }
          return 'property' in filterToApply
            ? { property: filterToApply.property }
            : { filterFn: filterToApply.filterFn }
        },
      ),
      filterOptions: vehicleFilterOptions,
    }
  },
)

export const getCarpoolVehiclesFilters = createSelectorWithStrictMode(
  getCarpoolActiveFilters,
  (activeFilters) => {
    const activeVehicleFilters = activeFilters.carpool
    const vehicleFilterOptions: MapCarpoolVehicleFilterOptions = {
      FREE: {
        icon: 'carpool_free',
        label: 'Free / Not Assigned',
        filterFn: ({ carpoolStatus }: VehicleWithDriver) =>
          match(carpoolStatus)
            .with(P.union(...BookingStatusMapping.free), () => true)
            .otherwise(() => false),
      },
      BOOKED: {
        icon: 'carpool_requested',
        label: 'Requested',
        filterFn: ({ carpoolStatus }: VehicleWithDriver) =>
          match(carpoolStatus)
            .with(P.union(...BookingStatusMapping.requested), () => true)
            .otherwise(() => false),
      },
      ACTIVE: {
        icon: 'carpool_active',
        label: 'Active',
        filterFn: ({ carpoolStatus }: VehicleWithDriver) =>
          match(carpoolStatus)
            .with(P.union(...BookingStatusMapping.active), () => true)
            .otherwise(() => false),
      },
      ISSUED: {
        icon: 'carpool_issued',
        label: 'Issued',
        filterFn: ({ carpoolStatus }: VehicleWithDriver) =>
          match(carpoolStatus)
            .with(P.union(...BookingStatusMapping.issued), () => true)
            .otherwise(() => false),
      },
    }

    return {
      type: 'carpool',
      value: {
        filters: mapValues(
          activeVehicleFilters,
          (
            _filterValue,
            untypedFilterKey,
          ):
            | { property: keyof VehicleWithDriver }
            | { filterFn: (v: VehicleWithDriver) => boolean } => {
            const filterKey = untypedFilterKey as keyof typeof activeVehicleFilters
            const filterToApply = vehicleFilterOptions[filterKey]

            return 'property' in filterToApply
              ? { property: filterToApply.property }
              : { filterFn: filterToApply.filterFn }
          },
        ),
        filterOptions: vehicleFilterOptions,
      },
    }
  },
)

export const getFilters = createSelectorWithStrictMode(
  getMapType,
  getVehiclesFilters,
  (_mapType, vehiclesFilters) => ({
    type: 'vehicles' as const,
    value: vehiclesFilters,
  }),
)

export const getFocusedItem = (state: AppState) => getFocusedVehicle(state)

export const getFocusedItemId = (state: AppState) => state.map.focusedItemId

export const getFocusedVehicleId = (state: AppState) => {
  const mapType = getMapType(state)
  if (
    mapType === 'vehicles' ||
    mapType === 'fleet' ||
    mapType === 'asset trackers' ||
    mapType === 'asset-trackers' ||
    mapType === 'svr-units'
  ) {
    return state.map.focusedItemId
  }

  return null
}

export const getFocusedGroup = (state: AppState) => {
  const focusedGroupId = getFocusedGroupId(state)
  if (!focusedGroupId) return null
  return getVehicleGroup(state, focusedGroupId)
}

function filterVehicles(
  vehicles:
    | ReturnType<typeof getAvailableVehiclesToShowInMapWithDrivers>
    | ReturnType<typeof getActiveVehiclesWithDrivers>,
  vehicleFilters: ReturnType<typeof getVehiclesFilters>['filters'],
  driverFilters: ReturnType<typeof getDriversFilters>['filters'],
  carpoolFilters: ReturnType<typeof getCarpoolVehiclesFilters>['value']['filters'],
  type: string | null | undefined,
  vehicleIconColorsPreference: ReadonlyArray<string>,
) {
  const hasFilters =
    !isEmpty(vehicleFilters) ||
    !isEmpty(driverFilters) ||
    !isEmpty(carpoolFilters) ||
    !isEmpty(vehicleIconColorsPreference)

  if (isEmpty(vehicles)) {
    return []
  }

  // if (ENV.FLAG_NEW_REDESIGN) {
  return toImmutable(
    vehicles.filter((vehicle) => {
      const { driver, defaultDriver, hardwareTypes, iconColor } = vehicle
      // Init conditional variables
      const hasDefaultDriver = !isEmpty(defaultDriver) && defaultDriver !== '0'
      const hasDriver = driver || hasDefaultDriver
      const hasDriverIdTag = !isEmpty(
        vehicle.alertsActions && vehicle.alertsActions.driverIDAlerts.driverIDTag,
      )

      if (isNil(type) || type in hardwareTypes) {
        if (hasFilters) {
          //match color
          if (
            !isEmpty(vehicleIconColorsPreference) &&
            !vehicleIconColorsPreference.includes(iconColor)
          ) {
            return false
          }

          const finalFilters = {
            ...vehicleFilters,
            ...driverFilters,
            ...carpoolFilters,
          }
          return Object.entries(finalFilters).every(([untypedKey, value]) => {
            const key = untypedKey as keyof typeof finalFilters

            if (value === 'not_used') {
              return true
            }

            if (typeof value === 'boolean') {
              if (
                (key === 'assignDriver' && hasDriver) ||
                hasDriverIdTag ||
                (key === 'noAssignDriver' && !hasDriver && !hasDriverIdTag)
              ) {
                return true
              } else {
                return false
              }
            }

            if ('filterFn' in value) {
              if (value.filterFn(vehicle)) {
                return true
              } else {
                return false
              }
            }

            const filterValue = vehicle[value.property]

            if (
              (key === 'noDriver' && !hasDriver && !hasDriverIdTag) ||
              (key === 'driver' && (hasDriver || hasDriverIdTag)) ||
              (typeof filterValue === 'string' && filterValue.includes(key))
            ) {
              return true
            } else {
              return false
            }
          })
        }
        return true
      }
      return false
    }),
  )
  // }

  // const filtered = vehicles.filter((vehicle) => {
  //   const { driver, defaultDriver, hardwareTypes, iconColor } = vehicle
  //   // Init conditional variables
  //   const hasDefaultDriver = !isNil(defaultDriver) && defaultDriver !== '0'
  //   const hasDriver = driver || hasDefaultDriver
  //   const hasDriverIdTag = !isNil(
  //     vehicle.alertsActions && vehicle.alertsActions.driverIDAlerts.driverIDTag,
  //   )

  //   if (isNil(type) || type in hardwareTypes) {
  //     if (hasFilters) {
  //       if (!isEmpty(vehicleIconColorsPreference)) {
  //         return vehicleIconColorsPreference.includes(iconColor)
  //       }

  //       const finalFilters = { ...vehicleFilters, ...driverFilters, ...carpoolFilters }
  //       return Object.entries(finalFilters).some(([untypedKey, value]) => {
  //         const key = untypedKey as keyof typeof finalFilters

  //         if (value === 'not_used') {
  //           return false
  //         }

  //         if (typeof value === 'boolean') {
  //           if (
  //             (key === 'assignDriver' && (hasDriver || hasDriverIdTag)) ||
  //             (key === 'noAssignDriver' && !hasDriver && !hasDriverIdTag)
  //           ) {
  //             return true
  //           }
  //           return false
  //         }

  //         if ('filterFn' in value) {
  //           return value.filterFn(vehicle)
  //         }

  //         const filterValue = vehicle[value.property]

  //         if (
  //           (key === 'noDriver' && !hasDriver && !hasDriverIdTag) ||
  //           (key === 'driver' && (hasDriver || hasDriverIdTag)) ||
  //           (typeof filterValue === 'string' && filterValue.includes(key))
  //         ) {
  //           return true
  //         }
  //         return
  //       })
  //     } else {
  //       return true
  //     }
  //   }
  //   return
  // })

  // return filtered
}

export const getMapVehicles = createSelectorWithStrictMode(
  getAvailableVehiclesToShowInMapWithDrivers,
  getHardwareFromType,
  getVehiclesFilters,
  getDriversFilters,
  getFocusedGroup,
  getStylePositionUnreliableTypeSetting,
  getCarpoolVehiclesFilters,
  getPreferences,
  (
    vehicles,
    type,
    vehiclesFilters,
    driversFilters,
    focusedGroup,
    stylePositionUnreliableType,
    carpoolFilters,
    { vehicleIconColors },
  ) => {
    const items = (() => {
      if (type === 'Fleet' && focusedGroup) {
        const group = reconciliateGroupItemIds(focusedGroup, vehicles)
        return group?.items || []
      }

      return vehicles.filter((v) => {
        // only show vehicles on map that have send gps location on the last X days
        if (!v.wasActiveInLastXDays) {
          return false
        }
        if (stylePositionUnreliableType === 'hidden') {
          return v.gpsFixType === 3
        }
        return true
      })
    })()

    return filterVehicles(
      items,
      vehiclesFilters.filters,
      driversFilters.filters,
      carpoolFilters.value.filters,
      type,
      vehicleIconColors,
    )
  },
)

export const getFilteredItems = createSelectorWithStrictMode(
  getVehiclesFilters,
  getDriversFilters,
  getHardwareFromType,
  getActiveVehiclesWithDrivers,
  getCarpoolVehiclesFilters,
  getPreferences,
  (
    mapVehiclesFilters,
    driversFilters,
    hardwareType,
    activeVehiclesWithDrivers,
    carpoolFilters,
    { vehicleIconColors },
  ) =>
    filterVehicles(
      activeVehiclesWithDrivers,
      mapVehiclesFilters.filters,
      driversFilters.filters,
      carpoolFilters.value.filters,
      hardwareType,
      vehicleIconColors,
    ),
)

export const getItems = createSelectorWithStrictMode(
  getMapType,
  getGroups,
  getFilteredItems,
  getFocusedGroup,
  getPreferences,
  (type, groups, items, focusedGroup, preferences) => {
    if (preferences.mapViewGroups[type]) {
      if (focusedGroup) {
        const group = reconciliateGroupItemIds(focusedGroup, items)
        return (group && group.items) || []
      }

      // Groups Enabled
      // Reconciliate all groups
      const reconciliatedGroups = !isEmpty(groups)
        ? groups.map((g) => ({
            ...reconciliateGroupItemIds(g, items),
            type: 'group' as const,
            itemType: type,
          }))
        : []

      // Return groups and remainder vehicles
      return toImmutable([...reconciliatedGroups, ...items])
    }

    // Groups Disabled
    return items
  },
)

export const getSortedLeftPanelExtras = createSelectorWithStrictMode(
  getLandmarks,
  getGeofences,
  (landmarks, geofences) => {
    const landmarksWithType = landmarks.map((l) => ({
      ...l,
      type: 'landmark' as const,
    }))
    const geofencesWithType = geofences.map((g) => ({
      ...g,
      type: 'geofence' as const,
    }))
    return [...landmarksWithType, ...geofencesWithType].sort((a, b) =>
      insensitiveCompare(a.name, b.name),
    )
  },
)

export const getFocusedLandmark = createSelectorWithStrictMode(
  getFocusedLandmarkId,
  getLandmarks,
  (focusedLandmarkId, landmarks) => {
    if (!focusedLandmarkId) {
      return null
    }

    return landmarks.find((l) => l.id === focusedLandmarkId) ?? null
  },
)

export const getMapLandmarksWithoutFocusedLandmark = createSelectorWithStrictMode(
  getFocusedLandmarkId,
  getMapLandmarks,
  (focusedLandmarkId, landmarks) => landmarks.filter((l) => l.id !== focusedLandmarkId),
)

export const getFocusedGeofence = createSelectorWithStrictMode(
  getFocusedGeofenceId,
  getGeofences,
  (focusedGeofenceId, geofences) => {
    if (!focusedGeofenceId) {
      return null
    }

    return geofences.find((g) => g.id === focusedGeofenceId) ?? null
  },
)

/* Start Timeline selectors */
export const getTimelinesData = (state: AppState) => getTripsSummaryPanelUI(state)

export const getTimelineProgress = (state: AppState) => state.map.timelineProgress
// - const getActiveEventIndex = (state: AppState) => state.map.activeEventIndex // just commented for now

export const getIsFirstTimeLoading = (state: AppState) => state.map.initialLoad
export const getTimelineSliderPlaying = (state: AppState) =>
  state.map.timelineSliderPlaying

/* End Timeline selectors */

export const getMapTypeId = ({ map: { mapTypeId } }: AppState) => mapTypeId

export const getContextMenuElement = (state: AppState) => state.map.contextMenuElement

export const getShouldDisplayVehicleLastKnownPosition = createSelectorWithStrictMode(
  getTimelineEventsByActivityAndMapType,
  (eventsByActivityAndMapType) => {
    if (eventsByActivityAndMapType.events.length === 0) {
      return true
    }

    let lastEventCoords = null
    if (eventsByActivityAndMapType.type === 'daily') {
      const lastEvent =
        eventsByActivityAndMapType.events[eventsByActivityAndMapType.events.length - 1]
      lastEventCoords = { lat: lastEvent.lat, lng: lastEvent.lng }
    } else {
      const lastEvent =
        eventsByActivityAndMapType.events[eventsByActivityAndMapType.events.length - 1]
      lastEventCoords = lastEvent.coords
    }

    if (
      lastEventCoords === null ||
      !isValidGPSData(lastEventCoords.lat, lastEventCoords.lng)
    ) {
      return true
    }

    return false
  },
)

export const getFocusedVehicleLastKnownPosition = (state: AppState) =>
  state.map.focusedVehicleLastKnownPosition

export const getIsMapLoading = createSelectorWithStrictMode(
  getIsTimelineBarUILoading,
  getIsTimelineEventsLoading,
  (isTimelineBarUILoading, isTimelineEventsLoading) =>
    isTimelineBarUILoading || isTimelineEventsLoading,
)

export const getMapViewVisibleRightPanel = (state: AppState) =>
  state.map.visibleRightPanel

export const getMapStateFromSaga = (state: AppState) => state.map
